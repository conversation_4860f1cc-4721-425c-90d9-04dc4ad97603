import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { AuthService } from '../auth.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class SafetyTrainingService {

  constructor(private apiService:ApiService, private authService: AuthService) {

  }
  async createSafetyTraining(data: any) {
    return await this.apiService.postData(ENDPOINTS.SAFETY_TRAINING.SAFETY_TRAINING, data);
  }
  async getSafetyTraining(data: any) {
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.SAFETY_TRAINING, data);
  }
  async getMaleFemaleCount(params: any) {
    // const businessUnitId = this.authService.getBusinessUnitId();
    // return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.MALE_FEMALE_COUNT, { params: { year: year, businessUnitId: businessUnitId } });
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.MALE_FEMALE_COUNT+'?'+params);
  }
  async  getTrainingTypeCount(params: any){
    // const businessUnitId = this.authService.getBusinessUnitId();
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.TRAINING_TYPE_COUNT+'?'+params);
  }
  async  getTrainingTypeCount1(params: any){
    // const businessUnitId = this.authService.getBusinessUnitId();
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.TRAINING_TYPE_COUNT+'?'+params);
  }

  async getParticipantTypeCount(params: any){
    // const businessUnitId = this.authService.getBusinessUnitId();
    // return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.PARTICIPANT_TYPE_COUNT, { params: { year: year, businessUnitId: businessUnitId } });
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.PARTICIPANT_TYPE_COUNT+'?'+params);

  }
  async getTrainingMonthWiseCount(params: any){
    // const businessUnitId = this.authService.getBusinessUnitId();
    // return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.TRAINING_MONTH_WISE_COUNT, { params: { year: year, businessUnitId: businessUnitId } });
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.TRAINING_MONTH_WISE_COUNT+'?'+params);

  }
  async getTrainingMonthWiseManHourCount(params: any){
    // const businessUnitId = this.authService.getBusinessUnitId();
    // return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.TRAINING_MONTH_WISE_MAN_HOUR_COUNT, { params: { year: year, businessUnitId: businessUnitId } });
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.TRAINING_MONTH_WISE_MAN_HOUR_COUNT+'?'+params);

  }
  async getAverageParticipantPerTraining(params: any){
    // const businessUnitId = this.authService.getBusinessUnitId();
    // return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.AVERAGE_PARTICIPANT_PER_TRAINING, { params: { year: year, businessUnitId: businessUnitId } });
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.AVERAGE_PARTICIPANT_PER_TRAINING+'?'+params);

  }
}
