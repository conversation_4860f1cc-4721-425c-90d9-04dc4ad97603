export const ENDPOINTS = {
  AUTH: {
    SEND_OTP: '/admins/superadmin-otp',
    VERIFY_OTP: '/admins/verify-otp',
    SIGNUP: 'admins/add-admin-web',
    LOGOUT: '/admins/logout',
    SAML_LOGIN: '/saml/login',
    SAML_VERIFY: '/admins/verify-saml-token',
  },

  UPDATE: {
    UPDATE_TABLE: '/update',
  },

  UPLOAD: {
    UPLOAD_FILE: '/common/upload'
  },

  ADMINS: {
    GET_ADMINS: '/admins',
    GET_ADMIN_STATS: '/admins/statistics',
    GET_ADMIN_BY_ID: (id: String) => `/admins/${id}`,
    GET_ADMIN_INFO: '/admins/admin-me',
    GET_ADMIN_BY_EMAIL: (email: String) => `/admins/${email}`,
    CHECK_ADMIN_EMAIL: '/admins/check-admin-email',
    CHECK_CONTACT_NUMBER: '/admins/check-contactNumber',
    GET_DELETED_USER: '/deleted-user-log',
    PLANT_TRANSFER_REQUEST: '/plant-transfer-request',
    ADMIN_ROLES: '/admins-roles',
    GET_PLANT_ADMIN: '/admins/get-plant-admin',
    PLANT_TRANSFER_UPDATE: '/plant-transfer-request/approve',
    ADMIN_DELETE: '/admins/delete-admin',
    DELETE: '/delete'
  },

  DELETE: {
    DELETE: '/delete'
  },

  QRCODE: {
    GET_QRCODE: '/qr-code',
    QR_TYPE: '/qr-type',
  },

  DEPARTMENTS: {
    GET_DEPARTMENTS: '/department',
  },

  DESIGNATIONS: {
    GET_DESIGNATION: '/designation',
  },

  DEVICES: {
    GET_DEVICES: '/device-detail',
  },

  PLANTS: {
    GET_PLANTS: '/plant',
    PLANT_TYPE: '/plant-type',
    QR_CODE_PLANT: '/qr-code/plant-pdf',
    REQUEST_PLANT_TRANSFER: '/plant-transfer-request/add',
  },

  TOUR: {
    GET_TOUR: '/tour',
    TOUR_SCAN_POINT: '/tourScanPoints'
  },

  OBSERVATION: {
    GET_OBSERVATION: '/observation',
    APPROVE_REWARD: 'observation/approve-reward'
  },

  DIGISAFE: {
    GET_DIGISAFE: '/digisafe',
    MIS_REPORT: '/digisafe/mis-report',
    CREATE: '/digisafe/create',
    MIS_COUNT: '/digisafe/mis-count',
    GMR_COUNT: '/digisafe/gmr-count',
  },

  OTHER_TASK: {
    GET_OTHER_TASK: '/other-task',
  },

  CRISIS_REPORT: {
    GET_CRISIS_REPORT: '/crisis-report',
    SPOKE_DETAIL: '/crisis-spoke',
    CREATE: '/crisis-report/add',
    SEND_EMAIL: '/crisis-report/sendmail'
  },

  LEADERBOARD: {
    GET_LEADERBOARD: '/admins/report/tour/userwise/leaderboard',
    GET_PLANT_LEADERS: '/admins/report/tour/userwise/leaderboard/topseven',
  },

  REPORT: {
    TOUR_USERWISE: '/admins/report/tour/userwise',
    TOUR_PLANTWISE: '/admins/report/tour/plantwise',
    ZONE_USERWISE: '/admins/report/zone/userwise',
    ZONE_PLANTWISE: '/admins/report/zone/plantwise',
    RAG_PLANTWISE: '/admins/report/qr/plantwise',
    RAG_QRWISE: '/admins/report/qr/qrwise',
    BOG_OBSERVATION_PLANTWISE: '/admins/report/observation/plantwise',
  },

  ZONE: {
    GET_ZONE: '/zone',
  },


  REWARD: {
    GET_REWARD: '/reward',
  },

  NOTIFICATION: {
    GET_NOTIFICATION: '/Notifications',
    SEND_CUSTOM_NOTIFICATION: '/Notifications/send-notification-plant-wise',
    GET_USERS_BY_PLANT_IDS: '/Notifications/get-user',
  },

  CLUSTER: {
    GET_CLUSTER: '/cluster',
  },

  SEGNMENT: {
    GET_SEGMENT: '/segment',
  },

  LOCATION: {
    LOCATION_TYPE: '/location-type',
    LOCATION: '/location',
  },

  OPCO: {
    GET_OPCO: '/opco',
    GET_FACILITY: '/plant-type'
  },

  RELATESTO: {
    GET_RELATESTO: '/relatesto',
  },

  AREA: {
    GET_AREA: '/area-master',
  },

  EQUIPMENT: {
    GET_EQUIPMENT_MASTER: '/equipement-master',
  },

  INCIDENT: {
    INCIDENT_MASTER: '/incident-master',
    INCIDENT_GET: '/incident',
  },

  BODY_PART: {
    BODY_PART_MASTER: '/body-part-master',
  },

  BUSINESS_UNIT: {
    GET_BUSINESS_UNIT: '/business-unit'
  },

  ROOT_CAUSE: {
    ROOT_CAUSE_MASTER: '/root-cause-master',
  },

  INSPECTION_TOOL: {
    INSPECTION_TOOL_MASTER: '/inspection-tool-master',
  },

  RECOMMENDED_TYPE: {
    RECOMMENDED_TYPE_MASTER: '/recomended-type-master',
  },

  DASHBOARD: {
    ADMIN_DASHBOARD: '/admins/admin-dashboard',
    CLUSTER_WISE_GRAPH: '/admins/report/observation/clusterWise',
    CLUSTER_WISE_STATUS: '/admins/report/observation/clusterWiseStatus',
    TOUR_OBSERVATION_COUNT: 'admins/tour-observation-count',
    PLANT_TOUR_COUNT: '/admins/clusterwise-plant-tour-count'
  },

  SETTINGS: {
    GET_SETTINGS: '/app-setting',
  },

  SAFETY_TRAINING: {
    SAFETY_TRAINING: '/training',
    MALE_FEMALE_COUNT: 'training/male-female-count',
    PARTICIPANT_TYPE_COUNT: 'training/participant-type-count',
    TRAINING_MONTH_WISE_COUNT: 'training/monthwise-training-count',
    // TRAINING_TYPE_COUNT: 'training/training-type-count',
    TRAINING_TYPE_COUNT: 'training/training-type-count-classroom',
    TRAINING_MONTH_WISE_MAN_HOUR_COUNT: 'training/monthwise-manhour-count',
    AVERAGE_PARTICIPANT_PER_TRAINING: 'training/monthwise-avg-count'
  },

  ADD_DJP: {
    CREATE_DJP: '/djp-qr-code/djp-create',
    GET_DJP: '/djp-qr-code',
    BULK_UPLOAD_EXCEL: '/djp-qr-code/bulkupload-xl',
    VALID_RECORDS: '/djp-qr-code/valid-recod',
    DJP_COMPLETE: '/djp-qr-code/djp-complete',
    UPDATE_DJP: '/djp-qr-code/update'

  }
}
