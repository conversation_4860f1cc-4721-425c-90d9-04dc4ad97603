import { Component, OnInit, ViewChild } from '@angular/core';
import { DashboardService } from '../../../services/dashboard/dashboard.service';
import { AuthService } from '../../../services/auth.service'; // Import AuthService
import { ChartComponent, NgApexchartsModule } from "ng-apexcharts";
import {
  ApexNonAxisChartSeries,
  ApexResponsive,
  ApexChart
} from "ng-apexcharts";
import { CommonModule } from '@angular/common';

export type ChartOptions = {
  series: ApexNonAxisChartSeries | any;
  chart: ApexChart | any;
  responsive: ApexResponsive[] | any;
  labels: any;
  title: any;
  dataLabels?: any;
  tooltip?: any;
  colors?: any;
  legend: any;
};

@Component({
  selector: 'app-cluster-wise-status',
  standalone: true, // Add standalone: true for standalone components
  imports: [NgApexchartsModule, CommonModule],
  templateUrl: './cluster-wise-status.component.html',
  styleUrls: ['./cluster-wise-status.component.scss']
})
export class ClusterWiseStatusComponent implements OnInit {

  chartInitializedStatus: boolean = false;
  @ViewChild("chart") chart!: ChartComponent;
  businessUnitId: string | null = null; // Declare businessUnitId property
  noDataAvailable: boolean = false;
  public chartOptions!: Partial<ChartOptions>;

  constructor(
    private dashboardService: DashboardService,
    private authService: AuthService // Inject AuthService
  ) {
    this.chartOptions = {
    }
  }

  ngOnInit() {
    this.businessUnitId = this.authService.getBusinessUnitId(); // Get businessUnitId
    this.getClusterWiseStatusGraph();
  }

  getClusterWiseStatusGraph() {
    const data: any = {}; // Initialize data as any to allow adding properties
    if (this.businessUnitId) {
      data.businessUnitId = this.businessUnitId; // Add businessUnitId to the body
    }
    this.dashboardService.getClusterWiseStatusGraph(data).then((res) => {
      if (res && res.responseCode === 200 && res.data && Array.isArray(res.data) && res.data.length > 0) {
        const clusterData = res.data[0];

        const closeCount = clusterData.closeCount != null ? Number(clusterData.closeCount) : null;
        const openCount = clusterData.openCount != null ? Number(clusterData.openCount) : null;

        if (
          (closeCount !== 0 && closeCount != null) ||
          (openCount !== 0 && openCount != null)
        ) {
          this.chartOptions.series = [
            closeCount,
            openCount
          ];

          this.chartOptions.labels = ['Open Count', 'Close Count'];
          this.chartOptions.dataLabels = {
            enabled: true,
            formatter: (value: number, options: { seriesIndex: number; w: any }) => {
              return options.w.config.series[options.seriesIndex];
            },
            style: {
              fontSize: '10px',
              fontWeight: '400',
              position: 'center',
              colors: ['#000000'],
            }
          }
          this.chartOptions.chart = {
            type: 'donut',
            height: 300,
            width: '302px'
          }
          this.chartOptions.legend = {
            position: 'bottom',
            horizontalAlign: 'center',
            floating: false,
            fontSize: '11px',
            // top:'255px',
            offsetY: -10, // Adjust as needed
            labels: {
              colors: ['#000'],
              useSeriesColors: false
            }
          };

          this.chartOptions.responsive = [
            {
              breakpoint: 180,
              options: {
                chart: {
                  width: 100,
                  height: 260
                },
                legend: {
                  position: 'center'
                }
              }
            }
          ],
            this.chartOptions.colors = ['#D18484CC', '#9BC39DCC'],
            this.chartInitializedStatus = true;
        } else {
          this.noDataAvailable = true;
          this.chartInitializedStatus = false;
          this.chartOptions = {};
          this.chartInitializedStatus = false;
        }
      } else {
        this.noDataAvailable = true;
        this.chartInitializedStatus = false;
        this.chartOptions = {};
        if (res && Array.isArray(res.data) && res.data.length === 0) {
          console.info('Cluster Wise Status data is empty.');
        } else {
          console.error('Unexpected response format or missing Cluster Wise Status', res);
        }
      }
    }).catch(error => {
      console.error('Error fetching Cluster Wise Status data', error);
    });
  }
}
