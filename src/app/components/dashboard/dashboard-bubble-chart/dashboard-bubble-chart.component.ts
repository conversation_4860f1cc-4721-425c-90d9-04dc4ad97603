import { Component, OnInit } from '@angular/core';
import { DashboardService } from '../../../services/dashboard/dashboard.service';
import { AuthService } from '../../../services/auth.service'; // Import AuthService
import { CommonModule } from '@angular/common';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config'; // Import createAxiosConfig

@Component({
    selector: 'app-dashboard-bubble-chart',
    standalone: true, // Add standalone: true for standalone components
    imports: [CommonModule],
    templateUrl: './dashboard-bubble-chart.component.html',
    styleUrls: ['./dashboard-bubble-chart.component.scss']
})
export class DashboardBubbleChartComponent implements OnInit {
    activatedQr: string = '';
    deActivatedQr: string = '';
    unregisteredQr: string = '';
    totalQrScan: string = '';
    bubbleData: any[] = [];
    activatedQrPercentage: number = 0;
    deActivatedQrPercentage: number = 0;
    unregisteredQrPercentage: number = 0;
    totalQrScanPercentage: number = 0;
    chartInitializedStatus: boolean = false;
    highlightedLegend: string | null = null;
    businessUnitId: string | null = null; // Declare businessUnitId property

    highlightLegendNumber(legendKey: string): void {
        this.highlightedLegend = legendKey;
    }

    removeHighlightLegendNumber(): void {
        this.highlightedLegend = null;
    }
    constructor(
        private dashboardService: DashboardService,
        private authService: AuthService // Inject AuthService
    ) { }

    ngOnInit() {
        this.businessUnitId = this.authService.getBusinessUnitId(); // Get businessUnitId
        this.loadBubbleChartData();
    }

    loadBubbleChartData() {
        const body: any = { // Initialize body as any to allow adding properties
            page: 1,
            limit: 10,
            sort: 'title,ASC',
            filter: ['enabled||eq||true']
        }
        if (this.businessUnitId) {
            body.businessUnitId = this.businessUnitId; // Add businessUnitId to the body
        }
        const params = createAxiosConfig(body); // Create params using createAxiosConfig

        this.dashboardService.getDashboard(params).then((res) => {
            if (res) {
                this.activatedQr = this.safeNumber(res.activatedQr);
                this.deActivatedQr = this.safeNumber(res.deActivatedQr);
                this.unregisteredQr = this.safeNumber(res.unregisterdQr);
                this.totalQrScan = this.safeNumber(res.noOfScan);


                const totalBase = 5000;
                const totalScanBase = 10000;
                this.activatedQrPercentage = this.calculateSafePercentage(res.activatedQr, res.tour);
                this.deActivatedQrPercentage = this.calculateSafePercentage(res.deActivatedQr, res.tour);
                this.unregisteredQrPercentage = this.calculateSafePercentage(res.unregisterdQr, res.tour);

                if (
                    this.activatedQrPercentage === 0 &&
                    this.deActivatedQrPercentage === 0
                ) {
                    this.unregisteredQrPercentage = 0;
                }
                this.bubbleData = [
                    { name: 'Activated QR', percentage: this.activatedQrPercentage, color: '#EFEAFE' },
                    { name: 'Deactivated QR', percentage: this.deActivatedQrPercentage, color: '#FCE5E8' },
                    { name: 'Unregistered QR', percentage: this.unregisteredQrPercentage, color: '#FFF7A4' },
                    { name: 'Total QR Scan', percentage: this.totalQrScanPercentage, color: '#E0F6ED' }
                ];


                this.bubbleData.sort((a, b) => b.percentage - a.percentage);
                this.chartInitializedStatus = true;
            }
        }).catch(error => {
            console.error('Error fetching cluster-wise data', error);
        });
    }

    private calculateSafePercentage(numerator: any, denominator: any): number {
        const num = parseFloat(numerator) || 0;
        const den = parseFloat(denominator) || 1;
        const result = (num / den) * 100;
        return Number.isFinite(result) ? parseFloat(result.toFixed(2)) : 0;
    }

    private safeNumber(value: string): any {
        const num = Number(value);
        return isNaN(num) ? 0 : num;
    }
}

