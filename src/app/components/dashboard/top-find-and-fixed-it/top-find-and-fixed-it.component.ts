import { Component, OnInit, ViewChild } from '@angular/core';
import { DashboardService } from '../../../services/dashboard/dashboard.service';
import { AuthService } from '../../../services/auth.service'; // Import AuthService
import { ChartComponent, NgApexchartsModule } from "ng-apexcharts";
import {
  ApexChart
} from "ng-apexcharts";
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';

export type ChartOptions = {
  series: ApexAxisChartSeries | any;
  chart: ApexChart | any;
  xaxis: ApexXAxis | any;
  dataLabels: ApexDataLabels | any;
  title: ApexTitleSubtitle | any;
  plotOptions: ApexPlotOptions | any;
  colors?: any;
};

@Component({
  selector: 'app-top-find-and-fixed-it',
  standalone: true, // Add standalone: true for standalone components
  imports: [NgApexchartsModule, CommonModule, FormsModule],
  templateUrl: './top-find-and-fixed-it.component.html',
  styleUrl: './top-find-and-fixed-it.component.scss'
})
export class TopFindAndFixedItComponent implements OnInit {

  chartInitializedStatus: boolean = false;
  @ViewChild("chart") chart!: ChartComponent;
  public chartOptions!: Partial<ChartOptions>;
  public selectedPeriod: 'monthly' | 'yearly' = 'monthly';
  businessUnitId: string | null = null; // Declare businessUnitId property
  noDataAvailable: boolean = false;
  constructor(
    private dashboardService: DashboardService,
    private authService: AuthService // Inject AuthService
  ) {
    this.chartOptions = {

    };
  }

  ngOnInit() {
    this.businessUnitId = this.authService.getBusinessUnitId(); // Get businessUnitId
    this.loadChartData();

  }

  loadChartData() {
    const currentDate = new Date();

    const formatYmd = (date: any) => date.toISOString().slice(0, 10);

    const calculateFromDate = (selection: any) => {
      const fromDate = new Date(currentDate);

      if (this.selectedPeriod === "monthly") {
        fromDate.setMonth(fromDate.getMonth() - 1);
      } else if (this.selectedPeriod === "yearly") {
        fromDate.setFullYear(fromDate.getFullYear() - 1);
      }

      return formatYmd(fromDate);
    };

    const from = calculateFromDate(this.selectedPeriod);
    const to = formatYmd(currentDate);

    const body: any = {
      from: from,
      to: to
    };
    if (this.businessUnitId) {
      body.businessUnitId = this.businessUnitId; // Add businessUnitId to the body
    }
    const params = createAxiosConfig(body);
    this.dashboardService.getDashboard(params).then((res) => {
      if (res && res.responseCode === 200 && res.topFindAndFixedIt && Array.isArray(res.topFindAndFixedIt) && res.topFindAndFixedIt.length > 0) {
        const topFindAndFixedItData = res.topFindAndFixedIt;
        const categories = topFindAndFixedItData.map((item: any) => item.name);
        const data = topFindAndFixedItData.map((item: any) => item.count);

        this.chartOptions = {
          series: [
            {
              name: 'Count',
              data: data
            }
          ],
          chart: {
            type: 'bar',
            height: 275,
          },
          colors: ['#1564A6'],
          title: {
            text: '',
            align: 'left'
          },
          xaxis: {
            categories: categories
          },
          dataLabels: {
            enabled: false,
            style: {
              fontSize: '11px',
            }
          },
          plotOptions: {
            bar: {
              columnWidth: '85%',
              borderRadius: [12],
            }
          },

        };
        this.chartInitializedStatus = true
      }
      else {
        this.chartInitializedStatus = false;
        this.chartOptions = {};
        this.noDataAvailable = true;
        if (res.topFindAndFixedIt && Array.isArray(res.topFindAndFixedIt) && res.topFindAndFixedIt.length === 0) {
          console.info('Top find it, fixed it data is empty.');
        } else {
          console.error('Unexpected response format or missing Top find it, fixed it', res);
        }
      }
    }).catch(error => {
      console.error('Error fetching Top find it, fixed it data', error);
    });
  }

  onPeriodChange() {
    this.chartInitializedStatus = false;
    this.loadChartData();
  }
}