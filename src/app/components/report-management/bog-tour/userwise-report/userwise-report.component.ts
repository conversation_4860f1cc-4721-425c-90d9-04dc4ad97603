import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, inject } from '@angular/core'; // Removed unused imports
import { FormsModule, NgForm } from '@angular/forms'; // Import FormsModule
import { ReportManagementService } from '../../../../services/report-management/report-management.service';
import { PaginationComponent } from "../../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../../shared/offcanvas/offcanvas.component";
import { NgSelectModule } from '@ng-select/ng-select'; // Import NgSelectModule
import { PlantManagementService } from '../../../../services/plant-management/plant-management.service'; // Import Plant service
import { createAxiosConfig } from '../../../../core/utilities/axios-param-config'; // If you use this helper
import { ClusterService } from '../../../../services/master-management/cluster/cluster.service'; // Keep if used elsewhere
import { AdminService } from '../../../../services/admin/admin.service'; // Import AdminService
import { ToastMessageComponent } from '../../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button and tooltips
import * as XLSX from 'xlsx'; // For Excel generation
import { NgSelectCheckboxComponent } from "../../../../shared/ng-select-checkbox/ng-select-checkbox.component"; // Adjust path if needed
import { DatePipe } from '@angular/common'; // Import DatePipe for date formatting

// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// Interfaces (adjust properties based on your actual data)
interface Cluster {
  id: number;
  name: string;
}

interface Plant {
  id: number;
  name: string;
  clusterId?: number;
}

interface UserReportItem {
    adminId: number;
    name: string;
    firstName: string;
    lastName: string;
    email: string;
    contactNumber: string;
    safeObservationCount: number;
    unSafeObservationCount: number;
    unSafeConditionObservationCount: number;
    temporaryQrCount: number;
    parmanentQrCount: number;
    ignoreQrCount: number;
    djpAssignCount: number;
    djpScanCount: number;
    noOfQrScanned: number;
    noOfZonesCovered: number;
    observationCount: number;
    fifiCount: number;
    startTime?: string | null;
    endTime?: string | null;
    difference?: string | null; // Added difference field from API response
    plantId?: number; // Added for potential use
    timeSpendCalculation?: string | null; // Added for time spent calculation
    lastQrScannedTime?: string | null; // Added for last QR scanned time
}

interface ReportFilter {
    startDate?: string | null;
    endDate?: string | null;
    plantIds?: number[] | null; // Allow multiple plant selections
    adminRoleId?: number | null;
    userId?: number[] | null; // Changed to array to support multiple user selection
    validTour?: boolean | null; // Tour filter: true for Valid Tour, false for All Tour
}

@Component({
    selector: 'app-userwise-report',
    standalone: true,
    imports: [
    CommonModule,
    FormsModule,
    NgSelectModule,
    PaginationComponent,
    OffcanvasComponent,
    NgbDropdownModule,
    NgbTooltipModule,
    ToastMessageComponent,
    NgSelectCheckboxComponent
],
    templateUrl: './userwise-report.component.html',
    styleUrl: './userwise-report.component.scss',
    providers: [DatePipe] // Add DatePipe provider
})
export class UserwiseReportComponent implements OnInit {
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    isEditUserModalOpen = false;
    list: UserReportItem[] = [];
    listLoading = true;

    currentPage = 1;
    itemsPerPage = 10; // Default page size
    totalItems = 0;

    filters: ReportFilter = {
        startDate: null,
        endDate: null,
        plantIds: null,
        adminRoleId: null,
        userId: null,
        validTour: false // Default to "All Tour"
    };

    // Role-Based Access Control Properties
    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = [];

    availablePlants: Plant[] = []; // Filtered based on role
    availableAdminRoles: any[] = [ { id: 3, name: 'User' }, { id: 2, name: 'Plant Admin' }, { id: 1, name: 'Super Admin' } ];
    availableUsers: any[] = [];
    userListLoading = false;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // Select All checkboxes state
    isAllPlantsSelected = false;
    isAllUsersSelected = false;

    // --- Inject Services ---
    private reportService = inject(ReportManagementService);
    private plantService = inject(PlantManagementService);
    private adminService = inject(AdminService);
    private datePipe = inject(DatePipe);
    isLoadingReport: boolean = false;

    constructor( ) { }

    /**
     * Custom search function for ng-select to search by first name, last name, or email.
     * @param term The search term entered by the user.
     * @param item The user object being evaluated.
     * @returns True if the item matches the search term, false otherwise.
     */
    customUserSearchFn(term: string, item: any): boolean {
        term = term.toLowerCase();
        const fullName = `${item.firstName ?? ''} ${item.lastName ?? ''}`.toLowerCase();
        const email = (item.email ?? '').toLowerCase();

        return fullName.includes(term) || email.includes(term);
    }

    /**
     * Convert difference string to hours with 2 decimal places
     * @param differenceStr Difference string from API
     * @returns Difference in hours with 2 decimal places, or null if invalid
     */
    convertDifferenceToHours(differenceStr: string | number | null | undefined): number | null {
        if (differenceStr === null || differenceStr === undefined) {
            return null;
        }

        try {
            // Parse the difference string/number to a number
            const differenceValue = typeof differenceStr === 'number' ? differenceStr : parseFloat(differenceStr);

            if (isNaN(differenceValue)) {
                return null;
            }

            // Convert to hours with 2 decimal places (assuming difference is in minutes)
            const diffHours = parseFloat((differenceValue / 60).toFixed(2));

            return diffHours;
        } catch (error) {
            console.error('Error converting difference to hours:', error);
            return null;
        }
    }

    ngOnInit() {
        this.setCurrentUserRoleAndDetailsById(); // Set role first
        this.setDefaultDates(); // Set default date range (1 month)
        this.initializeDefaultFilters(); // Initialize default filters based on role
    }

    /**
     * Initialize default filters based on user role and load data
     */
    async initializeDefaultFilters() {
        await this.getPlants(); // Fetch plants based on role

        // Set default plant selection (all available plants)
        if (this.availablePlants.length > 0) {
            this.filters.plantIds = this.availablePlants.map(plant => plant.id);
            this.isAllPlantsSelected = true;
            console.log('Default plant selection:', this.filters.plantIds);
        }

        // Set default role selection (all roles)
        this.filters.adminRoleId = null; // null means all roles
        console.log('Default role selection: All roles (null)');

        // Load users based on default plant and role selection
        await this.loadUsersForFilter();

        // Set default user selection (all available users)
        if (this.availableUsers.length > 0) {
            this.filters.userId = this.availableUsers.map(user => user.id);
            this.isAllUsersSelected = true;
            console.log('Default user selection:', this.filters.userId?.length, 'users');
        } else {
            console.warn('No users loaded for default selection');
        }

        // Load report data with default filters
        this.loadReportData(this.currentPage);
    }

    /**
     * Set default date range to last 30 days
     */
    setDefaultDates(): void {
        const today = new Date();
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(today.getDate() - 30); // Last 30 days

        // Format dates as YYYY-MM-DD for input fields
        this.filters.startDate = this.formatDateForInput(startDate);
        this.filters.endDate = this.formatDateForInput(endDate);

        console.log('Set default date range:', this.filters.startDate, 'to', this.filters.endDate);
    }

    /**
     * Format date as YYYY-MM-DD for input fields
     */
    formatDateForInput(date: Date): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1) { this.currentUserRole = ROLES.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) { this.toast?.showErrorToast("Plant Admin has no assigned plants."); }
            } else { this.currentUserRole = ''; this.toast?.showErrorToast("Invalid user role."); }
            console.log(`Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

    getCurrentListData(): UserReportItem[] | undefined {
        return this.list;
    }

    async fetchAllFilteredReports(): Promise<UserReportItem[] | null> {
        let plantIdsToSend: number[] | null = null;

        // Determine plant IDs based on role and *applied* filter
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                // If plant admin has selected specific plants from their assigned list, use those
                if (this.filters.plantIds && this.filters.plantIds.length > 0) {
                    // Ensure selected plants are within the assigned plants
                    plantIdsToSend = this.filters.plantIds.filter(id => this.loggedInPlantIds.includes(id));
                } else {
                    // Otherwise, use all assigned plants
                    plantIdsToSend = this.loggedInPlantIds;
                }
            } else { return []; } // No plants assigned
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            // Super admin uses selected plants, or null if none selected
            plantIdsToSend = (this.filters.plantIds && this.filters.plantIds.length > 0) ? this.filters.plantIds : null;
        } else { return null; } // Unknown role

        const payload: any = {
            pageSize: 1000000,
            pageIndex: 1,
        };
        if (this.filters.startDate) payload.startDate = this.filters.startDate;
        if (this.filters.endDate) payload.endDate = this.filters.endDate;
        if (plantIdsToSend) payload.plantIds = plantIdsToSend; // Send as array

        // Handle role filtering
        if (this.filters.adminRoleId !== null && this.filters.adminRoleId !== undefined) {
            // Specific role selected
            payload.adminsRoleIds = [this.filters.adminRoleId];
        } else {
            // "All roles" selected - include all role IDs (1, 2, 3)
            payload.adminsRoleIds = [1, 2, 3]; // Super Admin, Plant Admin, User
        }

        if (this.filters.userId && this.filters.userId.length > 0) payload.userIds = this.filters.userId; // Send user IDs as array

        // Add validTour filter for Excel export
        if (this.filters.validTour !== null && this.filters.validTour !== undefined) {
            payload.validTour = this.filters.validTour;
        }

        console.log('Request Payload for ALL Report Data:', payload);
        this.isLoadingReport = true; // Indicate loading
        try {
            const res = await this.reportService.tourUserWiseReport(payload);
            return res.data ?? [];
        } catch (error) {
            console.error("Error fetching all userwise reports for download:", error);
            this.toast?.showErrorToast("Failed to retrieve full data for download.");
            return null;
        } finally {
            this.isLoadingReport = false; // Stop loading
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return;

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} reports...`);

        let dataToExport: UserReportItem[] | null = null;

        try {
            if (type === 'all') { dataToExport = await this.fetchAllFilteredReports(); }
            else { dataToExport = this.getCurrentListData() ?? null; if (dataToExport === undefined) { dataToExport = null; } }

            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No reports available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} reports for Excel export (${type}).`);

            const dataForExcel = dataToExport.map(item => {
                const timeDiff = this.convertDifferenceToHours(item.difference);
                const timeSpentHours = item.timeSpendCalculation !== null && item.timeSpendCalculation !== undefined ? this.convertDifferenceToHours(item.timeSpendCalculation) : null;

                // Helper to safely parse numbers, defaulting to 0 if null, undefined, or NaN
                const parseNumeric = (value: any): number => {
                    const num = Number(value);
                    return isNaN(num) ? 0 : num;
                };

                return {
                    'User ID': parseNumeric(item.adminId),
                    'Plant Name': item.name,
                    'User Name': `${item.firstName ?? ''} ${item.lastName ?? ''}`.trim(),
                    'Email': item.email,
                    'Contact': item.contactNumber,
                    'Safe Act Obs': parseNumeric(item.safeObservationCount),
                    'Unsafe Act Obs': parseNumeric(item.unSafeObservationCount),
                    'Unsafe Condition Obs': parseNumeric(item.unSafeConditionObservationCount),
                    'Total Observation': parseNumeric(item.observationCount),
                    'FindItFixIt Count': parseNumeric(item.fifiCount),
                    'Total QR Scanned': parseNumeric(item.noOfQrScanned),
                    'Temporary QR Scanned': parseNumeric(item.temporaryQrCount),
                    'Permanent QR Scanned': parseNumeric(item.parmanentQrCount),
                    'Ignored QR': parseNumeric(item.ignoreQrCount),
                    'DJP Assign Count': parseNumeric(item.djpAssignCount),
                    'DJP Scan Count': parseNumeric(item.djpScanCount),
                    'Zones Covered': parseNumeric(item.noOfZonesCovered),
                    'Tour Start Time': this.formatDisplayDate(item.startTime),
                    'Last QR Scanned Time': this.formatDisplayDate(item.lastQrScannedTime),
                    'Tour End Time': this.formatDisplayDate(item.endTime),
                    'Difference (Hours)': timeDiff !== null ? timeDiff : 0, // Already a number or 0
                    'Time Spent (Hours)': timeSpentHours !== null ? timeSpentHours : 0, // Already a number or 0
                };
            });

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'UserwiseReport');

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `UserwiseReport_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false; this.downloadType = null;
        }
    }

    async getPlants(): Promise<void> {
        const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const param = createAxiosConfig(data);
        let allEnabledPlants: Plant[] = [];
        try {
            const response = await this.plantService.getPlants(param);
            allEnabledPlants = response?.data ?? response ?? [];
        } catch (error) {
            console.error("Error fetching plants:", error);
            this.availablePlants = [];
            this.toast?.showErrorToast("Failed to load plants list.");
            return;
        }

        // Filter based on role
        if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
            this.availablePlants = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            this.availablePlants = allEnabledPlants;
        } else {
            this.availablePlants = [];
        }
        console.log(`Loaded ${this.availablePlants.length} plants accessible to current user for filter.`);

        // Update Select All Plants checkbox state
        this.updateSelectAllPlantsState();
    }

    onPlantFilterChange(): void {
        console.log('Plant filter changed:', this.filters.plantIds);
        this.updateSelectAllPlantsState();
        this.filters.userId = null;
        this.isAllUsersSelected = false;
        this.availableUsers = [];
        this.loadUsersForFilter();
    }

    onRoleFilterChange(): void {
        console.log('Role filter changed:', this.filters.adminRoleId === null ? 'All roles' : this.filters.adminRoleId);
        this.filters.userId = null;
        this.isAllUsersSelected = false;
        this.availableUsers = [];

        // Load users based on the selected role (or all roles if null)
        this.loadUsersForFilter();
    }

    // Toggle Select All Plants checkbox
    toggleSelectAllPlants(event: Event): void {
        const checkbox = event.target as HTMLInputElement;
        if (checkbox.checked) {
            // Select all plants
            this.filters.plantIds = this.availablePlants.map(plant => plant.id);
            this.isAllPlantsSelected = true;
        } else {
            // Deselect all plants
            this.filters.plantIds = [];
            this.isAllPlantsSelected = false;
        }

        // When plant selection changes, reset user selection
        this.filters.userId = null;
        this.isAllUsersSelected = false;
        this.availableUsers = [];
        this.loadUsersForFilter();
    }

    // Update Select All Plants checkbox state based on current selection
    updateSelectAllPlantsState(): void {
        if (!this.filters.plantIds || this.filters.plantIds.length === 0) {
            this.isAllPlantsSelected = false;
        } else if (this.filters.plantIds.length === this.availablePlants.length) {
            this.isAllPlantsSelected = true;
        } else {
            this.isAllPlantsSelected = false;
        }
    }

    // Toggle Select All Users checkbox
    toggleSelectAllUsers(event: Event): void {
        const checkbox = event.target as HTMLInputElement;
        if (checkbox.checked) {
            // Select all users
            this.filters.userId = this.availableUsers.map(user => user.id);
            this.isAllUsersSelected = true;
        } else {
            // Deselect all users
            this.filters.userId = [];
            this.isAllUsersSelected = false;
        }
    }

    // Update Select All Users checkbox state based on current selection
    updateSelectAllUsersState(): void {
        if (!this.filters.userId || this.filters.userId.length === 0) {
            this.isAllUsersSelected = false;
        } else if (this.filters.userId.length === this.availableUsers.length) {
            this.isAllUsersSelected = true;
        } else {
            this.isAllUsersSelected = false;
        }
    }

    async loadUsersForFilter() {
        // Determine the plant IDs to filter users by
        let plantIdsToFilter: number[] | null = null;
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            // If Plant Admin has selected specific plants from their assigned list, use those
            if (this.filters.plantIds && this.filters.plantIds.length > 0) {
                // Ensure selected plants are within the assigned plants
                plantIdsToFilter = this.filters.plantIds.filter(id => this.loggedInPlantIds.includes(id));
            } else {
                // Otherwise, filter by all plants they own
                plantIdsToFilter = this.loggedInPlantIds;
            }
             // If Plant Admin has no plants or selected plants are not in assigned plants, don't fetch users
             if (!plantIdsToFilter || plantIdsToFilter.length === 0) {
                 this.availableUsers = [];
                 return;
             }
         } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
             // Super Admin filters by the selected plants, or null if none selected
             plantIdsToFilter = (this.filters.plantIds && this.filters.plantIds.length > 0) ? this.filters.plantIds : null;
         } else {
              this.availableUsers = []; // Unknown role
              return;
         }


        this.userListLoading = true;
        this.availableUsers = [];

        const payload: any = {};
        if (plantIdsToFilter) { payload.plantIds = plantIdsToFilter; } // Send as array

        // Handle role filtering
        if (this.filters.adminRoleId !== null && this.filters.adminRoleId !== undefined) {
            // Specific role selected
            payload.adminsRoleIds = [this.filters.adminRoleId];
        } else {
            // "All roles" selected - include all role IDs (1, 2, 3)
            payload.adminsRoleIds = [1, 2, 3]; // Super Admin, Plant Admin, User
            console.log('All roles selected, sending all role IDs:', payload.adminsRoleIds);
        }

        // Always fetch users based on role
        if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            if (Object.keys(payload).length === 0) {
                console.log('Super Admin: No specific filters, loading all active users.');
                // Add filter for active status
                payload.filter = ['enabled||eq||true', 'status||eq||1'];
            }
        } else if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (Object.keys(payload).length === 0 && this.loggedInPlantIds.length > 0) {
                console.log('Plant Admin: No specific filters, loading users for assigned plants.');
                payload.plantIds = this.loggedInPlantIds;
            } else if (Object.keys(payload).length === 0) {
                console.log('Plant Admin has no plants, skipping user fetch.');
                this.userListLoading = false;
                return;
            }
        } else {
            console.log('Unknown role or no relevant filters set, skipping user fetch.');
            this.userListLoading = false;
            return;
        }


        console.log('Fetching users with payload:', payload);

        try {
            // Assuming getPlantAdmin can handle filtering by plantIds and adminsRoleIds
            const response = await this.plantService.getPlantAdmin(payload);
            this.availableUsers = response.data ?? [];
            console.log('Available users loaded:', this.availableUsers);

            // Update Select All Users checkbox state
            this.updateSelectAllUsersState();
        } catch (error) {
            console.error("Error fetching users for filter:", error);
            this.availableUsers = [];
            this.isAllUsersSelected = false;
        } finally {
            this.userListLoading = false;
        }
    }

    openFilterModal() {
        // Reload users based on current filters when opening
        this.loadUsersForFilter();
        this.isEditUserModalOpen = true;
    }

    closeModal() { this.isEditUserModalOpen = false; }

    async loadReportData(page: number) {
        // Set loading state true immediately to prevent "no data" message
        this.listLoading = true;
        this.list = [];

        let plantIdsToSend: number[] | null = null;

        // Determine plant IDs based on role and filter selection
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                // If plant admin has selected specific plants from their assigned list, use those
                if (this.filters.plantIds && this.filters.plantIds.length > 0) {
                    // Ensure selected plants are within the assigned plants
                    plantIdsToSend = this.filters.plantIds.filter(id => this.loggedInPlantIds.includes(id));
                } else {
                    // Otherwise, use all assigned plants
                    plantIdsToSend = this.loggedInPlantIds;
                }
            } else {
                console.warn("Plant Admin has no plants, skipping report fetch.");
                this.totalItems = 0; this.listLoading = false; return;
            }
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            // Super admin uses selected plants, or null if none selected
            plantIdsToSend = (this.filters.plantIds && this.filters.plantIds.length > 0) ? this.filters.plantIds : null;
        } else {
             console.error("Unknown user role, cannot fetch report.");
             this.totalItems = 0; this.listLoading = false; return;
        }


        const payload: any = {
            pageSize: this.itemsPerPage,
            pageIndex: page,
        };
        if (this.filters.startDate) payload.startDate = this.filters.startDate;
        if (this.filters.endDate) payload.endDate = this.filters.endDate;
        if (plantIdsToSend) payload.plantIds = plantIdsToSend; // Send as array

        // Handle role filtering
        if (this.filters.adminRoleId !== null && this.filters.adminRoleId !== undefined) {
            // Specific role selected
            payload.adminsRoleIds = [this.filters.adminRoleId];
        } else {
            // "All roles" selected - include all role IDs (1, 2, 3)
            payload.adminsRoleIds = [1, 2, 3]; // Super Admin, Plant Admin, User
        }

        if (this.filters.userId && this.filters.userId.length > 0) payload.userIds = this.filters.userId; // Send user IDs as array

        // Add validTour filter
        if (this.filters.validTour !== null && this.filters.validTour !== undefined) {
            payload.validTour = this.filters.validTour;
        }

        console.log('Request Payload for Report:', payload);

        try {
            const res = await this.reportService.tourUserWiseReport(payload);
            this.totalItems = res.total ?? 0;
            this.list = res.data ?? [];
        } catch (error: any) {
            console.error("Error fetching userwise report:", error);
            this.totalItems = 0; this.list = [];
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load userwise report.');
        } finally {
            this.listLoading = false;
        }
    }

    onPageChange(page: number) {
        this.currentPage = page;
        this.listLoading = true; // Set loading immediately when changing pages
        this.loadReportData(this.currentPage);
    }

    applyFilters() {
        this.currentPage = 1;
        this.listLoading = true; // Set loading immediately when applying filters
        this.loadReportData(this.currentPage);
        this.closeModal();
    }

    /**
     * Format date string to a readable format
     * @param dateString ISO date string (e.g., 2025-04-16T00:00:29.000Z)
     * @returns Formatted date string (e.g., 16 Apr 2025, 05:30:29 AM)
     */
    formatDisplayDate(dateString: string | null | undefined): string {
        if (!dateString) return 'N/A';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Invalid Date';

            // Format date as "DD MMM YYYY, hh:mm:ss AM/PM"
            return this.datePipe.transform(date, 'dd MMM yyyy, hh:mm:ss a') || 'Invalid Date';
        } catch (e) {
            console.error('Error formatting date:', e);
            return 'Invalid Date';
        }
    }

    resetFilters() {
        this.filters = {
            startDate: null, endDate: null, plantIds: null,
            adminRoleId: null, userId: null, validTour: false
        };
        this.availableUsers = []; // Clear users as filters are reset

        // Reset Select All checkbox states
        this.isAllPlantsSelected = false;
        this.isAllUsersSelected = false;

        // Role filter applied implicitly in loadReportData
        this.currentPage = 1;
        this.listLoading = true; // Set loading immediately when resetting filters
        this.loadReportData(this.currentPage);
    }

    /**
     * Handle paste event for emails in the user select field
     * Parses pasted emails and automatically selects matching users
     * @param event ClipboardEvent containing pasted data
     */
    handlePaste(event: ClipboardEvent) {
        console.log('Data paste')

        const clipboardData = event.clipboardData
        if (!clipboardData) {
            return
        }

        const pastedData = clipboardData.getData('text').trim()

        // Split the pasted data by newlines, spaces, or commas
        // Use a more comprehensive regex to handle various formats
        const emails = pastedData.split(/[\s,;]+/).filter(email => {
            const trimmed = email.trim()
            return trimmed && trimmed.includes('@adani.com')
        })

        console.log(`Processing ${emails.length} pasted emails`)

        // Log available users for debugging
        console.log(`Available users count: ${this.availableUsers.length}`)
        if (this.availableUsers.length > 0) {
            console.log('Sample available users:', this.availableUsers.slice(0, 3).map(u => u.email))
        }

        const addedEmails: string[] = [] // Track added emails for feedback
        const notFoundEmails: string[] = [] // Track emails not found

        // Create a new array for user IDs to ensure change detection
        const currentUserIds = this.filters.userId ? [...this.filters.userId] : []

        // Process emails in batches to avoid UI freezing
        const processEmails = () => {
            // Create a map of lowercase emails to users for faster lookup
            const emailToUserMap = new Map()
            this.availableUsers.forEach(user => {
                if (user.email) {
                    emailToUserMap.set(user.email.toLowerCase(), user)
                }
            })

            // Loop through the pasted emails
            emails.forEach(email => {
                const trimmedEmail = email.trim().toLowerCase()
                const user = emailToUserMap.get(trimmedEmail)

                if (user) {
                    // User found, add ID if not already added
                    if (!currentUserIds.includes(user.id)) {
                        currentUserIds.push(user.id) // Add valid user ID
                        addedEmails.push(email) // Track added emails
                    } else {
                        console.log(`Email "${email}" already exists`)
                    }
                } else {
                    notFoundEmails.push(email)
                    console.log(`Email "${email}" not found in available users`)
                }
            })

            // Assign the new array to filters.userId to trigger change detection
            this.filters.userId = currentUserIds
            console.log(`Added ${addedEmails.length} emails, ${notFoundEmails.length} not found`)
            console.log('Updated user IDs count:', this.filters.userId?.length || 0)

            // Update the Select All Users checkbox state
            this.updateSelectAllUsersState()

            // Show feedback
            if (addedEmails.length > 0) {
                const totalSelected = this.filters.userId?.length || 0
                const message = addedEmails.length === 1
                    ? `Added user for email: ${addedEmails[0]} (${totalSelected} total selected)`
                    : `Added ${addedEmails.length} users from pasted emails (${totalSelected} total selected)`

                this.toast?.showSuccessToast(message)
            } else if (emails.length > 0) {
                if (notFoundEmails.length > 0) {
                    const sampleEmails = notFoundEmails.slice(0, 3).join(', ')
                    this.toast?.showErrorToast(`No emails matched available users. Examples: ${sampleEmails}`)
                } else {
                    this.toast?.showErrorToast('No valid emails were added. Make sure the emails exist in the user list.')
                }
            }
        }

        // Prevent the default paste behavior
        event.preventDefault()

        // Use setTimeout to avoid blocking the UI
        setTimeout(processEmails, 0)
    }
}
