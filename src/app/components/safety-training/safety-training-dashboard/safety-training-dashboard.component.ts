import { Component, OnInit, <PERSON>Child, inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexTitleSubtitle,
  ApexDataLabels,
  ApexStroke,
  ApexGrid,
  ApexPlotOptions,
  ApexYAxis,
  ApexLegend,
  ApexTooltip,
  ApexFill,
  ApexResponsive,
  NgApexchartsModule,
  ChartComponent
} from "ng-apexcharts";
import { SafetyTrainingService } from '../../../services/safety-training/safety-training.service';
import { OffcanvasComponent } from '../../../shared/offcanvas/offcanvas.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { ClusterService } from '../../../services/master-management/cluster/cluster.service';
import { OpcoService } from '../../../services/master-management/opco/opco.service';
import { PlantManagementService } from '../../../services/plant-management/plant-management.service';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';
import { NgSelectModule } from '@ng-select/ng-select';
// Define a type alias for cleaner chart options type
export type ChartOptions = {
  series: ApexAxisChartSeries | any[]; // Use any[] for pie charts
  chart: ApexChart;
  xaxis?: ApexXAxis;
  yaxis?: ApexYAxis | ApexYAxis[];
  title?: ApexTitleSubtitle;
  labels?: string[]; // Still needed for bar chart tooltips sometimes, but categories are primary
  stroke?: ApexStroke;
  dataLabels?: ApexDataLabels;
  fill?: ApexFill;
  legend?: ApexLegend;
  tooltip?: ApexTooltip;
  plotOptions?: ApexPlotOptions;
  grid?: ApexGrid;
  responsive?: ApexResponsive[];
  colors?: string[]; // Added for custom colors
};

// Interface for the raw safety training data (simplified for chart purposes)
interface SafetyTrainingData {
  month: string; // e.g., "2023-04-01"
  companyMaleParticipants: number;
  companyFemaleParticipants: number;
  trainingType: string; // "Internal" or "External"
  totalCompanyManhours: number;
  totalContractorManhours: number;
  totalCompanyEmployeeParticipants: number; // Added
  totalContractorEmployeeParticipants: number; // Added
  totalParticipants: number; // Added
}
interface SafetyTrainingFilter {
  month: string | null; // Using string for date input value
  facility: string | null;
  clusterId: string | null;
  companyName: string | null;
  plantId: string | null;
  trainingType: number | null;
  facultyName: string | null;
  topic: string | null;
  enabled?: string | null;
  sortDirection?: 'ASC' | 'DESC';
  sortField?: string;
  opcoId: string | null;
  plantTypeId: string | null;
  months?: any[]; // Array of month objects or IDs for multi-select
}
interface Plant {
  id: number;
  name: string;
  clusterId?: number;
  opcoId?: number;
  plantTypeId?: number;
}
const ROLES = {
  SUPER_ADMIN: 'super_admin',
  PLANT_ADMIN: 'plant_admin',
};
@Component({
  selector: 'app-safety-training-dashboard',
  standalone: true,
  imports: [CommonModule, NgApexchartsModule, OffcanvasComponent, FormsModule, ReactiveFormsModule, NgSelectModule], // Import NgApexchartsModule
  templateUrl: './safety-training-dashboard.component.html',
  styleUrls: ['./safety-training-dashboard.component.scss']
})
export class SafetyTrainingDashboardComponent implements OnInit {

  // Define properties for chart options
  @ViewChild("trainingTypeChart") chart!: ChartComponent;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  public monthlyTrainingsChartOptions: Partial<ChartOptions> | any;
  public genderDistributionChartOptions: Partial<ChartOptions> | any;
  public trainingTypeChartOptions: Partial<ChartOptions> | any;
  public manhoursChartOptions: Partial<ChartOptions> | any;
  public participantTypeChartOptions: Partial<ChartOptions> | any; // New Chart 5
  public avgParticipantsChartOptions: Partial<ChartOptions> | any; // New Chart 6
  private cdr = inject(ChangeDetectorRef);
  private opcoService = inject(OpcoService);
  private clusterService = inject(ClusterService);
  private plantService = inject(PlantManagementService);
  isLoading: boolean = false;
  isFilterModalOpen: boolean = false;
  availableFacilities: any[] = ['Manufacturing', 'Projects', 'RMX'];;
  availableClusters: any[] = [];
  availableCompanies: any[] = ['ACC', 'Ambuja', 'Sanghi', 'Penna', 'Orient', 'Adani'];
  availablePlants: Plant[] = [];
  loggedInPlantIds: number[] = [];
  currentUser: any; // Placeholder for current user data
  roleType: string = ''; // Placeholder for user role type
  currentUserRole: string = ''; // Placeholder for current user role
  loggedInAdminId: number | null = null; // Placeholder for logged-in admin ID
  public componentRoles = ROLES;
  filteredMonths: any[] =[];
  isAllMonthsSelected: boolean = false; // Flag to track if all plants are selected
  months = [
    { id: 1, name: 'January' },
    { id: 2, name: 'February' },
    { id: 3, name: 'March' },
    { id: 4, name: 'April' },
    { id: 5, name: 'May' },
    { id: 6, name: 'June' },
    { id: 7, name: 'July' },
    { id: 8, name: 'August' },
    { id: 9, name: 'September' },
    { id: 10, name: 'October' },
    { id: 11, name: 'November' },
    { id: 12, name: 'December' }
  ];
  year: number = new Date().getFullYear(); // Current year
  filters: SafetyTrainingFilter = {
    month: null,
    facility: null,
    clusterId: null,
    companyName: null,
    plantId: null,
    topic: null,
    trainingType: null,
    facultyName: null,
    opcoId: null,
    enabled: null,
    plantTypeId: null,
    months: []
  };
  uniqueYearsForFilter: any[] = []
  constructor(private safetyTrainingService: SafetyTrainingService) {
    // Initialize chart options with basic structure
    this.currentUser = localStorage.getItem('user');
    if (this.currentUser) {
      this.currentUser = JSON.parse(this.currentUser);
      this.roleType = this.currentUser?.adminsRole?.name;
      this.loggedInPlantIds = (Array.isArray(this.currentUser?.plantIds) && this.currentUser.plantIds.length > 0) ? this.currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
      // Retrieve and store the user's department ID
    } else {
      this.currentUser = null;
    }
    this.loggedInPlantIds = (Array.isArray(this.currentUser?.plantIds) && this.currentUser.plantIds.length > 0)
      ? this.currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
    this.initializeChartOptions();
  }

  ngOnInit(): void {
    // this.loadChartData();
    // this.getMaleFemaleTrainingCount()
    // this.getTrainingTypesCount()
    // this.getParticipantTypeCount()
    // this.getAvgParticipantsCount()
    // this.getMonthlyTrainingCount()
    // this.getManhoursCount()
    this.setFilters()
    this.getClusters();
    this.getFacilities();
    this.getPlants();
    this.getCurrentAndPreviousYearList()
    const currentYear = new Date().getFullYear();
    this.year = currentYear;
    this.onYearChange(currentYear);
  }
  getCurrentAndPreviousYearList() {
    const startYear = 2024;
    const currentYear = new Date().getFullYear();
    const years = [];

    for (let year = startYear; year <= currentYear; year++) {
      years.push(year);
    }
    this.uniqueYearsForFilter = years
  }
  initializeChartOptions(): void {
    this.monthlyTrainingsChartOptions = {
      series: [],
      chart: {
        type: 'bar', height: 350,
        toolbar: {
          show: false
        },
      },
      xaxis: { categories: [] },
      title: { text: 'Monthly Training Records' }, // Re-enabled title
      colors: ['#0b74b0'], // Adani Blue
      dataLabels: { enabled: false }
    };

    // Changed to Bar Chart
    this.genderDistributionChartOptions = {
      series: [],
      chart: {
        type: 'bar', height: 350,
        toolbar: {
          show: false
        },
      },
      plotOptions: { bar: { distributed: true } }, // Distribute colors to bars
      colors: ['#3498db', '#e74c3c'], // Blue, Red
      xaxis: { categories: ['Male', 'Female'] },
      title: { text: 'Company Participant Gender Distribution' }, // Re-enabled title
      legend: { show: false },
      dataLabels: { enabled: true }
    };

    // Changed to Bar Chart
    this.trainingTypeChartOptions = {
      series: [],
      chart: {
        type: 'pie', width: 350, height: 350,
        toolbar: {
          show: false
        },
      },
      labels: ['Classroom Training', 'Saksham Training'],
      // plotOptions: { bar: { distributed: true } }, // Distribute colors to bars
      colors: ['#2ecc71', '#f39c12'], // Green, Orange
      // xaxis: { categories: ['Internal Training', 'External Training'] },
      title: { text: 'Training Type Distribution' ,class: 'chart-title',align:'center'}, // Re-enabled title
      legend: { show: true, position: 'bottom' },
      dataLabels: {
        enabled: true,
        formatter: function (val: any, opts: any) {
          return val.toFixed(1) + '%';
        }
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 150
            },
            legend: {
              position: "bottom"
            }
          }
        },
        {
          breakpoint: 768,
           options: {
            chart: {
              width: 250
            },
          }
        },
        {
          breakpoint: 1024,
          options: {
            chart: {
              width: 350
            },
          },
           title:{
            style: {
              fontSize: '11px !important',
           }
          }
        }
      ]
    };

    this.manhoursChartOptions = {
      series: [],
      chart: {
        type: 'bar', height: 350,stacked: true,
        toolbar: {
          show: false
        },
      },
      plotOptions: { bar: { horizontal: false } },
      colors: ['#75479c', '#bd3681'], // Adani Purple, Pink
      xaxis: { categories: [] },
      yaxis: { title: { text: 'Manhours' } },
      title: { text: 'Monthly Manhours (Company vs Contractor)', align: 'center', 
  }, 
      legend: { position: 'bottom' },
      fill: { opacity: 1 },
      dataLabels: { enabled: false }   
    };

    // Changed to Bar Chart
    // this.participantTypeChartOptions = {
    //   series: [],
    //   chart: {
    //     type: 'bar', height: 350,
    //     toolbar: {
    //       show: false
    //     },
    //   },
    //   plotOptions: { bar: { distributed: true } }, // Distribute colors to bars
    //   colors: ['#1abc9c', '#9b59b6'], // Turquoise, Purple
    //   xaxis: { categories: ['Company', 'Contractor'] },
    //   title: { text: 'Participant Type Distribution (Overall)' }, // Re-enabled title
    //   legend: { show: false },
    //   dataLabels: { enabled: true }
    // };
    this.participantTypeChartOptions = {
      series: [],
      chart: {
        type: 'donut',
        height: 350, width: 350

      },
      title: { text: 'Participant Type Distribution (Overall)',align:'center' }, // Re-enabled title
      labels: ['Company', 'Contractor'],
      dataLabels: {
        enabled: true,
        //        style: {
        //   colors: ['#000000']  // sets the label color to black
        // }
      },
      legend: {
        position: 'bottom'
      },
      fill: {
        type: 'gradient'
      },
      colors: ['#008FFB', '#00E396'],
      plotOptions: {
        pie: {
          donut: {
            size: '60%',
            labels: {
              show: true,
              total: {
                show: true,
                label: 'Total',
                formatter: () => '100%'
              }
            }
          }
        }
      }
    }

    // New Chart 6: Average Participants per Training (Monthly)
    this.avgParticipantsChartOptions = {
      series: [],
      chart: {
        type: 'line', height: 350,
        toolbar: {
          show: false
        },
      },
      colors: ['#e67e22'], // Dark Orange
      xaxis: { categories: [] },
      yaxis: { title: { text: 'Avg. Participants' } },
      title: { text: 'Average Participants per Training (Monthly)' }, // Re-enabled title
      stroke: { curve: 'smooth' },
      dataLabels: { enabled: false },
      markers: {
    size: 4
  },
    };
  }

  loadChartData(): void {
    this.isLoading = true;
    // Simulate fetching data
    // Replace this with actual API call to fetch safety training records
    const mockData: SafetyTrainingData[] = [
      { month: '2023-01-01', companyMaleParticipants: 15, companyFemaleParticipants: 5, trainingType: 'Internal', totalCompanyManhours: 40, totalContractorManhours: 20, totalCompanyEmployeeParticipants: 20, totalContractorEmployeeParticipants: 10, totalParticipants: 30 },
      { month: '2023-02-01', companyMaleParticipants: 12, companyFemaleParticipants: 8, trainingType: 'External', totalCompanyManhours: 30, totalContractorManhours: 25, totalCompanyEmployeeParticipants: 20, totalContractorEmployeeParticipants: 15, totalParticipants: 35 },
      { month: '2023-03-01', companyMaleParticipants: 20, companyFemaleParticipants: 3, trainingType: 'Internal', totalCompanyManhours: 50, totalContractorManhours: 15, totalCompanyEmployeeParticipants: 23, totalContractorEmployeeParticipants: 5, totalParticipants: 28 },
      { month: '2023-03-01', companyMaleParticipants: 10, companyFemaleParticipants: 10, trainingType: 'Internal', totalCompanyManhours: 40, totalContractorManhours: 10, totalCompanyEmployeeParticipants: 20, totalContractorEmployeeParticipants: 8, totalParticipants: 28 }, // Second training in March
      { month: '2023-04-01', companyMaleParticipants: 10, companyFemaleParticipants: 2, trainingType: 'Internal', totalCompanyManhours: 24, totalContractorManhours: 36, totalCompanyEmployeeParticipants: 12, totalContractorEmployeeParticipants: 18, totalParticipants: 30 },
      { month: '2023-05-01', companyMaleParticipants: 18, companyFemaleParticipants: 7, trainingType: 'External', totalCompanyManhours: 45, totalContractorManhours: 30, totalCompanyEmployeeParticipants: 25, totalContractorEmployeeParticipants: 20, totalParticipants: 45 },
      { month: '2023-06-01', companyMaleParticipants: 22, companyFemaleParticipants: 6, trainingType: 'Internal', totalCompanyManhours: 55, totalContractorManhours: 22, totalCompanyEmployeeParticipants: 28, totalContractorEmployeeParticipants: 12, totalParticipants: 40 },
    ];

    setTimeout(() => {
      this.processChartData(mockData);
      this.isLoading = false;
    }, 1000); // Simulate network delay
  }

  processChartData(data: SafetyTrainingData[]): void {
    // 1. Monthly Training Count
    const monthlyCounts: { [key: string]: number } = {};
    const monthlyTotals: { [key: string]: { totalParticipants: number, count: number } } = {}; // For Avg Participants

    data.forEach(record => {
      const monthYear = new Date(record.month).toLocaleString('default', { month: 'short', year: 'numeric' });
      // Count trainings
      monthlyCounts[monthYear] = (monthlyCounts[monthYear] || 0) + 1;
      // Sum participants and count for average calculation
      if (!monthlyTotals[monthYear]) {
        monthlyTotals[monthYear] = { totalParticipants: 0, count: 0 };
      }
      monthlyTotals[monthYear].totalParticipants += record.totalParticipants;
      monthlyTotals[monthYear].count += 1;
    });
    const sortedMonths = Object.keys(monthlyCounts).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    this.monthlyTrainingsChartOptions.series = [{ name: 'Trainings', data: sortedMonths.map(month => monthlyCounts[month]) }];
    this.monthlyTrainingsChartOptions.xaxis = { categories: sortedMonths };


    // 2. Gender Distribution (Bar Chart)
    let totalMale = 0;
    let totalFemale = 0;
    data.forEach(record => {
      totalMale += record.companyMaleParticipants;
      totalFemale += record.companyFemaleParticipants;
    });
    // Format for bar chart series
    this.genderDistributionChartOptions.series = [{ name: 'Participants', data: [totalMale, totalFemale] }];

    // 3. Training Type Distribution (Bar Chart)
    let internalCount = 0;
    let externalCount = 0;
    data.forEach(record => {
      if (record.trainingType.toLowerCase() === 'internal') {
        internalCount++;
      } else if (record.trainingType.toLowerCase() === 'external') {
        externalCount++;
      }
    });
    // Format for bar chart series
    this.trainingTypeChartOptions.series = [{ name: 'Count', data: [internalCount, externalCount] }];

    // 4. Manhours by Type (Monthly)
    const monthlyManhours: { [key: string]: { company: number, contractor: number } } = {};
    data.forEach(record => {
      const monthYear = new Date(record.month).toLocaleString('default', { month: 'short', year: 'numeric' });
      if (!monthlyManhours[monthYear]) {
        monthlyManhours[monthYear] = { company: 0, contractor: 0 };
      }
      monthlyManhours[monthYear].company += record.totalCompanyManhours;
      monthlyManhours[monthYear].contractor += record.totalContractorManhours;
    });
    const sortedManhourMonths = Object.keys(monthlyManhours).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    this.manhoursChartOptions.series = [
      { name: 'Company', data: sortedManhourMonths.map(month => monthlyManhours[month].company) },
      { name: 'Contractor', data: sortedManhourMonths.map(month => monthlyManhours[month].contractor) }
    ];
    this.manhoursChartOptions.xaxis = { categories: sortedManhourMonths };

    // 5. Participant Type Distribution (Bar Chart)
    let totalCompanyParticipants = 0;
    let totalContractorParticipants = 0;
    data.forEach(record => {
      totalCompanyParticipants += record.totalCompanyEmployeeParticipants;
      totalContractorParticipants += record.totalContractorEmployeeParticipants;
    });
    // Format for bar chart series
    this.participantTypeChartOptions.series = [{ name: 'Participants', data: [totalCompanyParticipants, totalContractorParticipants] }];

    // 6. Average Participants per Training (Monthly)
    const avgParticipantsData = sortedMonths.map(month => {
      const monthlyData = monthlyTotals[month];
      return monthlyData.count > 0 ? parseFloat((monthlyData.totalParticipants / monthlyData.count).toFixed(2)) : 0;
    });
    this.avgParticipantsChartOptions.series = [{ name: 'Avg Participants', data: avgParticipantsData }];
    if(sortedMonths.length === 1) {
      this.avgParticipantsChartOptions.chart.type = 'bar'
    }
    else{
      this.avgParticipantsChartOptions.chart.type = 'line';
    }
    this.avgParticipantsChartOptions.xaxis = { categories: sortedMonths };

    // Important: Trigger change detection if needed, although direct assignment usually works
    this.cdr.detectChanges();
  }
  getMaleFemaleTrainingCount(queryString: any) {
    // this.safetyTrainingService.getMaleFemaleCount(this.year).then((res: any) =>
    this.safetyTrainingService.getMaleFemaleCount(queryString).then((res: any) => {
      this.genderDistributionChartOptions.series = [{ name: 'Participants', data: [res.data[0].male, res.data[0].female] }]
      this.cdr.detectChanges();
    })

  }
  // getTrainingTypeCount() {
  //   this.safetyTrainingService.getTrainingTypeCount(this.year).then((res: any) => {
  //     // this.trainingTypeChartOptions.series = [{ name: 'Count', data: [res.data[0].internal, res.data[0].external] }]
  //     this.trainingTypeChartOptions.series = [Number(res.data[0].internal), Number(res.data[0].external)];
  //     // this.trainingTypeChartOptions.labels = ['Internal Training', 'External Training'];
  //     this.cdr.detectChanges();
  //   })

  // }
  getTrainingTypesCount(queryString: any) {
    this.safetyTrainingService.getTrainingTypeCount(queryString).then((res: any) => {
      this.trainingTypeChartOptions.series = [Number(res.data[0].classroom), Number(res.data[0].saksham)];
      this.cdr.detectChanges();
    })

  }
  getParticipantTypeCount(queryString: any) {
    this.safetyTrainingService.getParticipantTypeCount(queryString).then((res: any) => {
      // this.participantTypeChartOptions.series = [{ name: 'Participants', data: [res.data[0].companyParticipant, res.data[0].contractParticipant] }]
      this.participantTypeChartOptions.series = [Number(res.data[0].companyParticipant), Number(res.data[0].contractParticipant)]
      this.cdr.detectChanges();
    })

  }
  getManhoursCount(queryString: any) {
    this.safetyTrainingService.getTrainingMonthWiseManHourCount(queryString).then((response: any) => {
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      const categories = response.data.map((item: any) => monthNames[item.month - 1]);
      console.log(categories);
      const companyManhours = Object.values(response.data).map((item: any) => item.companyEmpManhour.toFixed(2));
      const contractorManhours = Object.values(response.data).map((item: any) => item.contractEmpManhour.toFixed(2));
      this.manhoursChartOptions.series = [{ name: 'Company', data: companyManhours },
      { name: 'Contractor', data: contractorManhours }]
      this.manhoursChartOptions.xaxis = { categories: categories };
      this.cdr.detectChanges();
    })

  }
  getAvgParticipantsCount(queryString: any) {
    this.safetyTrainingService.getAverageParticipantPerTraining(queryString).then((response: any) => {
      const counts = Object.values(response.data).map((item: any) => item.averageParticipants);
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      const categories = response.data.map((item: any) => monthNames[item.month - 1]);

      this.avgParticipantsChartOptions.series = [{ name: 'Trainings', data: counts }]
      if(categories.length === 1) {
      this.avgParticipantsChartOptions.chart.type = 'bar'
    }
    else{
      this.avgParticipantsChartOptions.chart.type = 'line';
    }
      this.avgParticipantsChartOptions.xaxis = { categories: categories };
      this.cdr.detectChanges();
    })

  }
  getMonthlyTrainingCount(queryString: any) {
    this.safetyTrainingService.getTrainingMonthWiseCount(queryString).then((response: any) => {
      const counts = Object.values(response.data).map((item: any) => item.count);
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      const categories = response.data.map((item: any) => monthNames[item.month - 1]);

      this.monthlyTrainingsChartOptions.series = [{ name: 'Trainings', data: counts }]
      this.monthlyTrainingsChartOptions.xaxis = { categories: categories };
      console.log(this.monthlyTrainingsChartOptions)
      this.cdr.detectChanges();
    })

  }
  openFilterModal(): void {
    this.isFilterModalOpen = true;
  }

  closeFilterModal(): void {
    this.isFilterModalOpen = false;
  }
  applyFilters(): void {
    console.log("Applying filters:", this.filters);
    this.setFilters()
    // Implement filtering logic here
    // this.currentPage = 1; // Reset to first page on filter
    // this.getTrainingData()
    this.closeFilterModal();
  }
  resetFilters(): void {
    this.filters = {
      month: null,
      facility: null,
      clusterId: null,
      companyName: null,
      plantId: null,
      topic: null,
      trainingType: null,
      facultyName: null,
      opcoId: null,
      plantTypeId: null,
      months: []
    };
    this.year = new Date().getFullYear(); // Reset to current year
    this.isAllMonthsSelected = false;
    this.setFilters()
  }
  async getClusters(): Promise<void> {
    try {
      const clusterParams = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(clusterParams);
      const response = await this.clusterService.getCluster(axiosConfig);
      this.availableClusters = response?.data ?? response ?? [];
    } catch (error) {
      console.error("Error fetching clusters:", error);
      this.availableClusters = [];
      // this.toast?.showErrorToast('Failed to load cluster list.');
    }
  }

  async getFacilities(): Promise<void> {
    console.log('Fetching companies (OpCos)...');
    try {
      const companyParams = { sort: 'title,ASC', filter: ['enabled||eq||true', 'isDeleted||eq||false'], limit: 1000 };
      const axiosConfig = createAxiosConfig(companyParams);
      const response = await this.opcoService.getFacility(axiosConfig);
      this.availableFacilities = response?.data ?? response ?? [];
    } catch (error) {
      console.error("Error fetching companies:", error);
      this.availableCompanies = [];
      // this.toast?.showErrorToast('Failed to load company list.');
    }
  }

  async getPlants(): Promise<void> {
    const plantParams: any = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };

    // Add cluster filter if clusterId is selected
    //  if (this.filters.clusterId !== null && this.filters.clusterId !== undefined) {
    //    plantParams.filter.push(`clusterId||eq||${this.filters.clusterId}`);
    //    console.log("Filtering plants by Cluster ID:", this.filters.clusterId);
    //  }

    let allEnabledPlants: Plant[] = [];
    try {
      const axiosConfig = createAxiosConfig(plantParams);
      const response = await this.plantService.getPlants(axiosConfig);
      allEnabledPlants = response?.data ?? response ?? [];

      if (!Array.isArray(allEnabledPlants)) {
        console.error("Received non-array response for plants:", allEnabledPlants);
        allEnabledPlants = [];
      }
    } catch (error) {
      console.error("Error fetching plants:", error);
      this.availablePlants = [];
      // Assuming a toast service is available or implement simple console error
      // this.toast?.showErrorToast('Failed to load plant list.');
    }

    // Filter plants for PLANT_ADMIN based on assigned plant IDs
    if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
      console.log("Filtering plants for PLANT_ADMIN. Allowed Plant IDs:", this.loggedInPlantIds);
      allEnabledPlants = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
      console.log("Filtered plants for PLANT_ADMIN:", allEnabledPlants.map(p => p.name));
    }

    this.availablePlants = allEnabledPlants;
    // Reset plant multi-select if available plants change significantly
    //  this.filters.plantIds = null;
    //  this.isAllMonthsSelected = false;
  }
  setCurrentUserRoleAndDetailsById(): void {
    // Keep original implementation
    try {
      const userString = localStorage.getItem('user');
      if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
        console.error("User data not found or is invalid in localStorage."); this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = []; this.toast?.showErrorToast("User session invalid. Please log in again."); return;
      }
      const currentUser = JSON.parse(userString); this.loggedInAdminId = currentUser?.id ?? null;
      this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0) ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
      // Retrieve and store the user's department ID
      // this.loggedInUserDepartmentId = currentUser?.departmentId ?? null;
      // console.log(`User Department ID: ${this.loggedInUserDepartmentId || 'None'}`); // Log the department ID
      const roleId = currentUser?.adminsRoleId; if (roleId === 1) { this.currentUserRole = this.componentRoles.SUPER_ADMIN; }
      else if (roleId === 2) { this.currentUserRole = this.componentRoles.PLANT_ADMIN; if (this.loggedInPlantIds.length === 0) { console.error(`Plant Admin (Role ID ${roleId}, User ID ${this.loggedInAdminId}) has no plants assigned!`); this.toast?.showErrorToast("User configuration error: Plant Admin has no assigned plants."); } }
      else { console.error(`Invalid or missing adminsRoleId (${roleId}) for User ID ${this.loggedInAdminId}.`); this.currentUserRole = ''; this.toast?.showErrorToast("User configuration error: Invalid role."); }
      console.log(`User Role Determined: ${this.currentUserRole || 'None'}, Plant IDs: [${this.loggedInPlantIds.join(', ')}]`);
      // this.loadSafetyTrainingRecords()
    } catch (error) { console.error("Error parsing user data from localStorage:", error); this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = []; this.toast?.showErrorToast("Error reading user session. Please log in again."); }
  }

  setFilters() {
    const filterParams: string[] = [];

    // --- Apply Role-Based Plant Filtering ---
    if (this.currentUser.adminsRole.name === 'plantadmin') {
      if (this.loggedInPlantIds.length > 0) {
        if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
          // Plant Admin selected a specific plant from their assigned list
          // Assuming UI prevents selecting unassigned plants, directly filter by the selected plant ID
          filterParams.push(`plantId=${this.filters.plantId}`);
          console.log("Plant Admin: Filtering tasks by selected plant ID:", this.filters.plantId);
        } else {
          // Plant Admin selected "All My Plants" (or default) - filter by all their assigned plants
          filterParams.push(`plantId=${this.loggedInPlantIds[0]}`);
          console.log("Plant Admin: Filtering tasks by assigned plant IDs:", this.loggedInPlantIds);
        }
      } else {
        // Plant Admin has no plants assigned - apply a filter that returns no data
        console.warn("Plant Admin has no assigned plants. Applying impossible filter.");
        filterParams.push(`plantId=-1`);
      }
    } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
      // Super Admin: Only filter if they explicitly selected a plant
      if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
        filterParams.push(`plantId=${this.filters.plantId}`);
        console.log("Super Admin: Filtering tasks by selected plant ID:", this.filters.plantId);
      } else {
        console.log("Super Admin: No specific plant selected, showing all.");
        // No plant filter added
      }
    } else {
      // No role or unknown role - potentially restrict or show all? Showing all for now.
      console.warn("Unknown user role, not applying specific plant filters.");
      // No plant filter added by default
    }
    // // --- End Role-Based Plant Filtering ---

    // if (this.filters.enabled !== null && this.filters.enabled !== undefined) { filterParams.push(`enabled||eq||${this.filters.enabled}`); }
    // if (this.filters.opcoId) { filterParams.push(`&opcoId=${this.filters.opcoId}`); }
    if (this.filters.plantTypeId) { filterParams.push(`plantTypeId=${this.filters.plantTypeId}`); }
    if (this.filters.clusterId) { filterParams.push(`clusterId=${this.filters.clusterId}`); }
    if (this.filters.companyName) { filterParams.push(`companyName=${this.filters.companyName}`); }
    if (this.filters.plantId) { filterParams.push(`plantId=${this.filters.plantId}`); }
    if (this.filters.months && this.filters.months.length > 0) {
      console.log("Selected Months:", this.filters.months);
      filterParams.push(`month=[${this.filters.months}]`);
      // const dates = this.getDateRangeFromMonths(this.filters.months, this.year);
      // filterParams.push(`startDate=${dates?.startDate}`)
      // filterParams.push(`endDate=${dates?.endDate}`)
      // console.log(this.getDateRangeFromMonths(this.filters.months, this.year))
    }


    // if (this.filters.month) { filterParams.push(`month||eq||${this.filters.month}`); }

    // if (this.filters.facility) { filterParams.push(`facility||eq||${this.filters.facility}`); }
    filterParams.push(`year=${this.year}`); // Always filter by current year
    // const param = createAxiosConfig(filterParams);
    const queryString = filterParams.join('&');
    console.log("Filter Parameters Set:", queryString);
    //  this.getTrainingTypesCount()
    this.getTraningData(queryString)
    this.getMaleFemaleTrainingCount(queryString)
    // this.getTrainingTypesCount(queryString)
    this.getParticipantTypeCount(queryString)
    this.getManhoursCount(queryString)
    this.getAvgParticipantsCount(queryString)
    this.getMonthlyTrainingCount(queryString)
  }

  getTraningData(queryString: any) {
    this.safetyTrainingService.getTrainingTypeCount1(queryString).then((res: any) => {
      this.trainingTypeChartOptions.series = [Number(res.data[0].classroom), Number(res.data[0].saksham)];
      this.cdr.detectChanges();
    })
  }
  updateSelectAllState(): void {
    const numAvailable = this.filteredMonths.length;
    const numSelected = this.filters.months?.length ?? 0;

    if (numSelected === 0) {
      this.isAllMonthsSelected = false;
    } else if (numSelected === numAvailable && numAvailable > 0) {
      this.isAllMonthsSelected = true;
    } else {
      this.isAllMonthsSelected = false; // Treat as unchecked for standard checkbox
    }
  }
  toggleSelectAllPlants(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    if (checkbox.checked) {
      this.filters.months = this.filteredMonths.map(p => p.id);
      this.isAllMonthsSelected = true;
    } else {
      this.filters.months = [];
      this.isAllMonthsSelected = false;
    }
    this.updateSelectAllState(); // Keep checkbox state consistent
  }
  getDateRangeFromMonths(months: any, year: any) {
    if (!months || months.length === 0) return null;

    // Sort months to get the earliest and latest
    const sortedMonths = months.slice().sort((a: any, b: any) => a - b);
    const firstMonth = sortedMonths[0];
    const lastMonth = sortedMonths[sortedMonths.length - 1];

    // Start Date: first day of the first selected month in startYear
    const startDate = new Date(year, firstMonth - 1, 1); // JS months: 0-indexed

    // End Date: last day of the last selected month in endYear
    const endDate = new Date(year, lastMonth, 0); // day 0 of next month = last day of current month

    const formatDate = (date: any) => {
      const yyyy = date.getFullYear();
      const mm = String(date.getMonth() + 1).padStart(2, '0'); // Months start at 0
      const dd = String(date.getDate()).padStart(2, '0');
      return `${yyyy}-${mm}-${dd}`;
    };

    return {
      startDate: formatDate(startDate),
      endDate: formatDate(endDate)
    };
  }
  
  onYearChange(selectedYear: number): void {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // getMonth is zero-based

    if (selectedYear == currentYear) {
      this.filteredMonths = this.months.filter(month => month.id <= currentMonth);
    } else {
      this.filteredMonths = [...this.months];
    }
  }
}
