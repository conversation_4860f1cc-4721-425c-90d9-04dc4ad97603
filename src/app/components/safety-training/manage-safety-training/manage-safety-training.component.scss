.card-body{
  height: 70vh;
  overflow: auto;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

.table-responsive {
  overflow-x: auto;
  white-space: nowrap;
  max-width: 100%;
}

table {
  min-width: 900px;
  border-collapse: collapse;
}

.table-header th {
  font-size: 12px;
  background-color: #0B74B0;
  color: white;
  font-weight: bold;
  text-align: center;
  padding: 8px 5px;
  border: 1px solid #0B74B0;
}

td {
  font-size: 12px;
  padding: 0.25rem 0.5rem !important;
  vertical-align: top;
  border: 1px solid #dee2e6;
}

i.edit {
  font-size: 12px;
}

.actions {
  text-align: center !important;
  vertical-align: middle !important;
}

.btn-edit {
  width: 32px;
  height: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Filter button */
.filter-button {
  width: 35px;
  cursor: pointer;
}

/* Button text color */
.adani-btn {
  color: white !important;
}

.details-cell {
  max-width: 200px;
  vertical-align: middle !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  padding: 8px; /* Add consistent padding */
}
.details-cell-1{
    max-width: 130px;
  vertical-align: middle !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  padding: 8px; /* Add consistent padding */
}
.details-cell-2{
  max-width: 90px;
  vertical-align: middle !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  padding: 8px; /* Add consistent padding */
}
/* Styling for details */
.details-container {
  width: 100%;
  text-align: left;
  padding: 0;
}

.label-value {
  display: block;
  width: 100%;
  margin-bottom: 2px;
  padding-bottom: 2px;
  border-bottom: 1px dotted #ddd;
  line-height: 1.2;
  position: relative;
  padding-right: 40px;
}

.label-value:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label-value strong {
  font-weight: 500;
  color: #777;
  font-size: 12px;
  text-align: left;
  margin-right: 15px;
  min-width: 80px;
  display: inline-block;
}

.value-text {
  font-weight: 600;
  color: #222;
  font-size: 12px;
  text-align: left;
  position: absolute;
  right: 0;
}

/* Style for bold text in table cells to match value-text */
td b,
td strong,
td span:not(.info-label) {
  font-weight: 600;
  color: #222;
  font-size: 12px;
  letter-spacing: normal;
  line-height: 1.5;
}

/* Add bottom border to table rows */
.table-bordered tbody tr {
  border-bottom: 1px dashed #eee;
}

.label-2{
  display: grid; grid-template-columns:35% 65%;text-align: right;
  padding-right: 0px !important;
  padding-bottom: 0px !important;
  margin-bottom: 0px !important;

}
.value-2{
  font-weight: 600;
  color: #222;
  font-size: 12px;
  text-align: left;
  right: 0;
}
.text-overflow {
  display: inline-block;
  max-width: 200px; /* Or whatever fits your layout */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: right;
}
.remark-text {
    display: -webkit-box;
    -webkit-line-clamp: 4; /* Limits to 4 lines */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    // max-width: 80px !important; /* Maintain your max-width */
}
