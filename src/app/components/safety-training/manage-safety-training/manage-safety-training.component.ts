import { Component, OnInit, ViewChild, inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, FormsModule, NgForm } from '@angular/forms';
import { OffcanvasComponent } from '../../../shared/offcanvas/offcanvas.component';
import { PaginationComponent } from '../../../shared/pagination/pagination.component';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';
import { SafetyTrainingService } from '../../../services/safety-training/safety-training.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { OpcoService } from '../../../services/master-management/opco/opco.service';
import { ClusterService } from '../../../services/master-management/cluster/cluster.service';
import { PlantManagementService } from '../../../services/plant-management/plant-management.service';
import * as XLSX from 'xlsx';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { MatTooltip } from '@angular/material/tooltip';

// Interface for Safety Training Record data
interface SafetyTrainingRecord {
  id?: number; // Assuming an ID for editing/deleting
  month: string | null;
  facility: string | null;
  type: number | null;
  status: number | null;
  adminId: number | null;
  clusterId: string | null;
  companyName: string | null;
  opcoId: string | null;
  plantTypeId:string | null;
  plantType?: any;
  plantId: string | null;
  date: string | null; // Using string for date input value
  topic: string | null;
  trainingType: number | null;
  facultyName: string | null;
  companyMaleManagement: number | null;
  companyMaleSFA: number | null;
  totalCompanyMale: number | null;
  companyFemaleSFA: number | null;
  companyFemaleManagement: number | null;
  // totalCompanyFemale: number | null;
  totalCompanyFemale: number | null;
  totalNoOfCompanyEmpParticipant: number | null;
  contractMale: number | null;
  contractFemale: number | null;
  totalContractEmpParticipant: number | null;
  totalParticpant: number | null;
  trainingProgDuration: number | null;
  companyEmpManhour: number | null;
  contractEmpManhour: number | null;
  totalManHour: number | null;
  remark: string | null;
  plant?: any;
  cluster?: any;
  opco?: any;
  lastUpdatedByAdminId?:any;
  lastUpdatedByAdmin?:any;
  admin?:any;
}


// Interface for Filter form data
interface SafetyTrainingFilter {
  month: string | null; // Using string for date input value
  facility: string | null;
  clusterId: string | null;
  companyName: string | null;
  plantId: string | null;
  trainingType: number | null;
  facultyName: string | null;
  topic:string | null;
  enabled?: string | null;
  sortDirection?: 'ASC' | 'DESC';
  sortField?: string;
  opcoId: string | null;
  plantTypeId: string | null;
  // Add other filter properties as needed
}
interface Plant {
  id: number;
  name: string;
  clusterId?: number;
  opcoId?: number;
  plantTypeId?: number;
}
const ROLES = {
  SUPER_ADMIN: 'super_admin',
  PLANT_ADMIN: 'plant_admin',
};

@Component({
  selector: 'app-manage-safety-training',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    OffcanvasComponent,
    PaginationComponent,
    ToastMessageComponent,
    NgbDropdownModule,
    MatTooltip
  ],
  templateUrl: './manage-safety-training.component.html',
  styleUrl: './manage-safety-training.component.scss'
})
export class ManageSafetyTrainingComponent implements OnInit {

  @ViewChild('createForm') createForm?: NgForm;
  @ViewChild('editForm') editForm?: NgForm;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  public componentRoles = ROLES;
  safetyTrainingRecords: SafetyTrainingRecord[] = [];
  isLoading: boolean = false;
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;
  isFilterModalOpen = false;
  isCreateModalOpen = false;
  isEditModalOpen = false;

  isDownloadingExcel = false;
  downloadType: 'current' | 'all' | null = null;

  filters: SafetyTrainingFilter = {
    month: null,
    facility: null,
    clusterId: null,
    companyName: null,
    plantId: null,
    topic: null,
    trainingType: null,
    facultyName: null,
    opcoId: null,
    enabled: null,
    plantTypeId: null,
  };

  newRecordData: SafetyTrainingRecord = this.getInitialFormData();
  editingRecordData: SafetyTrainingRecord | null = null;
  createLoading = false;
  editLoading = false;
  minDate: string='';
  maxDate: string='';
  dateErrorMessage: string = ''; 
  // Properties for dropdown options (initially empty)
  availableFacilities: any[] = ['Manufacturing', 'Projects', 'RMX'];;
  availableClusters: any[] = [];
  availableCompanies: any[] = ['ACC', 'Ambuja', 'Sanghi', 'Penna', 'Orient', 'Adani'];
  availablePlants: Plant[] = [];
  loggedInPlantIds: number[] = [];
  currentUser: any; // Placeholder for current user data
  private cdr = inject(ChangeDetectorRef);
  private safetyTrainingService = inject(SafetyTrainingService);
  private opcoService = inject(OpcoService);
  private clusterService = inject(ClusterService);
  private plantService = inject(PlantManagementService);
  loggedInAdminId: number | null = null;
  currentUserRole: string ='';
  roleType: string | null = null;
  adminData:any
  constructor() {
    this.currentUser = localStorage.getItem('user');
    if (this.currentUser) {
      this.currentUser = JSON.parse(this.currentUser);
      this.roleType = this.currentUser?.adminsRole?.name;
      this.loggedInPlantIds = (Array.isArray(this.currentUser?.plantIds) && this.currentUser.plantIds.length > 0) ? this.currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
      // Retrieve and store the user's department ID
    } else {
      this.currentUser = null;
    }
      this.loggedInPlantIds = (Array.isArray(this.currentUser?.plantIds) && this.currentUser.plantIds.length > 0)
                ? this.currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
    console.log("Current User:", this.currentUser);
  }

  ngOnInit(): void {
    const today = new Date();
    this.maxDate = today.toISOString().split('T')[0];
    // Changed to allow last 2 months instead of just 1 month
    const twoMonthsAgo = today.getMonth() - 2;
    let targetMonth = twoMonthsAgo;
    let targetYear = today.getFullYear();
    
    // Handle year boundary crossing
    if (twoMonthsAgo < 0) {
      targetMonth = 12 + twoMonthsAgo; // This will be 10 or 11 for Nov/Dec
      targetYear = today.getFullYear() - 1;
    }
    
    const firstDayOfTwoMonthsAgo = new Date(targetYear, targetMonth, 1);
    this.minDate = this.formatDate(firstDayOfTwoMonthsAgo);
    
    // Initial data load
    this.getTrainingData()
    this.setCurrentUserRoleAndDetailsById()
    if(this.currentUser.adminsRole.name== 'plantadmin'){
      this.getPlantAdminData()
    }
    // this.loadSafetyTrainingRecords();
    // Load dropdown options (placeholder)
    // this.loadDropdownOptions();
  }

  formatDate(date: Date, formate?:string): string {
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const day = ('0' + date.getDate()).slice(-2);
    if(formate == 'dd-mm-yyyy'){
      return `${month}-${month}-${year}`;
    }
    else if(formate == 'yyyy-mm-dd'){
      return `${year}-${month}-${day}`;
    }
    else{
      return `${year}-${month}-${day}`;
    }
   
  }
  getInitialFormData(): SafetyTrainingRecord {
    return {
      id: 0, // Assuming an ID for editing/deleting
      month: null,
      facility: null,
      type: null,
      status: null,
      adminId: null,
      clusterId: null,
      companyName: null,
      opcoId: null,
      plantTypeId: null,
      plantType: null,
      plantId: null,
      date: null,
      topic: null,
      trainingType: null,
      facultyName: null,
      companyMaleManagement: null,
      companyMaleSFA: null,
      totalCompanyMale: null,
      companyFemaleSFA: null,
      companyFemaleManagement: null,
      totalCompanyFemale: null,
      totalNoOfCompanyEmpParticipant: null,
      contractMale: null,
      contractFemale: null,
      totalContractEmpParticipant: null,
      totalParticpant: null,
      trainingProgDuration: null,
      companyEmpManhour: null,
      contractEmpManhour: null,
      totalManHour: null,
      remark: null,
    };
  }


  async loadSafetyTrainingRecords(): Promise<void> {
    // Keep original implementation
    await Promise.all([this.getPlants(), this.getClusters(), this.getFacilities()]);
    // await this.loadSafetyTrainingRecords();
  }

  async downloadExcel(type: 'current' | 'all'): Promise<void> {
    const dataToExport = type === 'current' ? this.safetyTrainingRecords : await this.getAllTrainingData();

    if (!dataToExport || dataToExport.length === 0) {
      this.toast?.showErrorToast('No data available to download.');
      return;
    }

    console.log(`Attempting to download ${type} data as Excel...`);
    this.isDownloadingExcel = true;
    this.downloadType = type;

    try {
      // Prepare data for Excel
      const dataForExcel = dataToExport.map(record => ({
        'Training Type': record.type == 0 ? 'Classroom Training' :'Saksham Training',
        'Month': record.date ? new Date(record.date).toLocaleString('en-US', { month: 'short', year: 'numeric' }) : 'N/A',
        'Facility': record.plantType?.title ? record.plantType?.title :'N/A',
        'Cluster': record.cluster?.title ? record.cluster?.title :'N/A',
        'Company': record.companyName ? record.companyName :'N/A',
        'Plant': record.plant?.name ? record.plant?.name :'N/A',
        'Date': record.date ? new Date(record.date).toLocaleDateString('en-GB') : 'N/A',
        'Topic': record.topic,
        'Faculty Type': record.trainingType == 0 ? 'Internal' : 'External',
        'Faculty Name': record.facultyName,
        'Duration': record.trainingProgDuration,
        'Company Male (Mgmt)': record.companyMaleManagement,
        'Company Male (SFA)': record.companyMaleSFA,
        'Total Company Male': record.totalCompanyMale,
        'Company Female (Mgmt)': record.companyFemaleManagement,
        'Company Female (SFA)': record.companyFemaleSFA,
        'Total Company Female': record.totalCompanyFemale,
        'Total Company Participants': record.totalNoOfCompanyEmpParticipant,
        'Contractor Male': record.contractMale,
        'Contractor Female': record.contractFemale,
        'Total Contractor Participants': record.totalContractEmpParticipant,
        'Total Participants': record.totalParticpant,
        'Company Manhours': record.companyEmpManhour,
        'Contractor Manhours': record.contractEmpManhour,
        'Total Manhours': record.totalManHour,
        'Remarks / Feedback': record.remark,
      }));

      // Generate Excel workbook
      const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'SafetyTrainingRecords');

      // Convert to binary string
      const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });

      // Create Blob and download
      const data: Blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });
      const fileName = `SafetyTrainingRecords_${type === 'current' ? 'Page' + this.currentPage : 'All'}_${new Date().toLocaleDateString('en-GB').replace(/\//g, '-')}.xlsx`;

      // Manual download using a temporary link
      const link = document.createElement('a');
      link.href = URL.createObjectURL(data);
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(link.href); // Clean up the URL object

      console.log("Excel file generated and download initiated.");
      this.toast?.showSuccessToast('Excel file downloaded successfully!');

    } catch (error: any) {
      console.error("Error downloading Excel file:", error);
      this.toast?.showErrorToast(error?.message || 'Failed to download Excel file.');
    } finally {
      this.isDownloadingExcel = false;
      this.downloadType = null;
    }
  }

  async getAllTrainingData(): Promise<SafetyTrainingRecord[] | null> {
    // This method is specifically for fetching ALL data for export, not for table display
    const filterParams: string[] = [];

    // --- Apply Role-Based Plant Filtering ---
    if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
      if (this.loggedInPlantIds.length > 0) {
        if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
          filterParams.push(`plantId||eq||${this.filters.plantId}`);
        } else {
          filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
        }
      } else {
        filterParams.push(`plantId||eq||-1`);
      }
    } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
      if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
        filterParams.push(`plantId||eq||${this.filters.plantId}`);
      }
    }

    if (this.filters.enabled !== null && this.filters.enabled !== undefined) { filterParams.push(`enabled||eq||${this.filters.enabled}`); }
    if (this.filters.opcoId) { filterParams.push(`opcoId||eq||${this.filters.opcoId}`); }
    if (this.filters.plantTypeId) { filterParams.push(`plantTypeId||eq||${this.filters.plantTypeId}`); }
    if (this.filters.clusterId) { filterParams.push(`clusterId||eq||${this.filters.clusterId}`); }
    if (this.filters.companyName) { filterParams.push(`companyName||eq||${this.filters.companyName}`); }
    // if (this.filters.month) { filterParams.push(`month||eq||${this.filters.month}`); }


    const dataRequest = {
      limit: 10000, // Fetch all data for download
      sort: 'id,DESC',
      filter: filterParams,
    };

    try {
      const param = createAxiosConfig(dataRequest);
      console.log('API Request Params (Safety Training Download - All Data):', JSON.stringify(param, null, 2));
      const response = await this.safetyTrainingService.getSafetyTraining(param);
      // Handle potential response structures
      if (response && Array.isArray(response.data)) {
        return response.data; // Assuming the data is in the 'data' property
      } else if (response && Array.isArray(response)) {
        return response; // Handle direct array response
      } else {
        console.warn("Received unexpected response structure when fetching all safety training records:", response);
        return null;
      }
    } catch (error: any) {
      console.error("Error fetching all safety training records for download:", error);
      this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
      return null;
    }
  }

  // Placeholder for loading dropdown options
  // loadDropdownOptions(): void {
  //   // Simulate loading options
  //   this.availableFacilities = ['Manufacturing', 'Projects', 'RMX'];
  //   this.availableClusters = ['North', 'South', 'East', 'West'];
  //   this. = ['ACC', 'AMBUJA', 'Sanghi', 'Penna', 'Orient'];
  //   this.availablePlants = ['Plant A', 'Plant B', 'Plant C'];
  // }

  // Offcanvas methods
  openFilterModal(): void {
    this.isFilterModalOpen = true;
  }

  closeFilterModal(): void {
    this.isFilterModalOpen = false;
  }

  openCreateModal(): void {
    this.newRecordData = this.getInitialFormData(); // Reset form for new record
    this.isCreateModalOpen = true;
    // Optional: Reset form validation state after modal opens
    setTimeout(() => this.createForm?.resetForm(this.newRecordData), 0);
  }

  closeCreateModal(): void {
    // console.log('confirm form',this.createForm)

    console.log('confirm form', this.createForm?.value)
    console.log('newRecordData', this.newRecordData)  
    console.log('finaldata',{...this.createForm?.value,...this.newRecordData})
    this.isCreateModalOpen = false;
  }

  openEditModal(record: SafetyTrainingRecord): void {
    this.editingRecordData = { ...record }; // Create a copy for editing
    this.isEditModalOpen = true;
    // Optional: Reset form validation state after modal opens

    setTimeout(() => this.editForm?.resetForm(this.editingRecordData), 0);
  }

  closeEditModal(): void {
    console.log('confirm form',this.editForm?.value)
    this.isEditModalOpen = false;
    this.editingRecordData = null; // Clear editing data
  }

  // Placeholder for filter application
  applyFilters(): void {
    console.log("Applying filters:", this.filters);
    // Implement filtering logic here
    this.currentPage = 1; // Reset to first page on filter
    this.getTrainingData()
    this.closeFilterModal();
  }

  // Placeholder for filter reset
  resetFilters(): void {
    this.filters = {
      month: null,
      facility: null,
      clusterId: null,
      companyName: null,
      plantId: null,
      topic: null,
      trainingType: null,
      facultyName: null,
      opcoId: null,
      plantTypeId: null,
    };
    console.log("Filters reset.");
    this.currentPage = 1; // Reset to first page
    this.getTrainingData(); // Reload data without filters
  }

  // Placeholder for pagination change
  onPageChange(page: number): void {
    console.log("Page changed to:", page);
    this.currentPage = page;
    this.getTrainingData(); // Reload data for the new page
  }
  trimFormControls(formGroup: FormGroup) {
  Object.keys(formGroup.controls).forEach(key => {
    const control = formGroup.get(key);
    if (control && control.value && typeof control.value === 'string') {
      control.setValue(control.value.trim());
    }
  });
}
  
  async submitEditForm(): Promise<void> {
    console.log('hello')
    this.trimFormControls(this.editForm!.form);
    this.editForm?.form.markAllAsTouched();
    if (this.editForm?.invalid || this.dateErrorMessage) {
      console.log("Edit form invalid or no data.");
      return;
    }
    const additionalData = {
      id: this.editingRecordData?.id,
      enabled: true,
      isDeleted: false,
      // type: 0,
      status: 0,
      // adminId: this.currentUser?.id,
      lastUpdatedByAdminId: this.currentUser?.id,

    };
    // console.log("Submitting edit form with data:", this.editForm?.value);
    // console.log("Additional data:", this.newRecordData);
    // const apiPayload = {
    //   ...this.editForm?.value,
    //   ...additionalData,
    // };
    let apiPayload = {}
      if (this.currentUser.adminsRole.name == 'plantadmin') {
        
        let data = {
          plantTypeId: this.editingRecordData?.plantTypeId,
          plantId: this.editingRecordData?.plantId,
          clusterId: this.editingRecordData?.clusterId,
          companyName: this.editingRecordData?.companyName,
        }

          const isAnyValueEmpty = Object.values(data).some(value => value === null || value === undefined || value === '');
  
          if (isAnyValueEmpty) {
            console.error('Error: One or more required fields are missing in original record data.', data);
            this.toast?.showErrorToast('Original record is missing required facility, cluster, plant or company information.');
            return;
          }
        apiPayload = {
          ...this.editForm?.value,
          ...additionalData,
          ...data,
        };
      }
    else {
      apiPayload = {
        ...this.editForm?.value,
        ...additionalData,
      };
    }

    console.log(`Submitting Edit. Final API Payload:`, JSON.stringify(apiPayload, null, 2));

    try {
      const response = await this.safetyTrainingService.createSafetyTraining(apiPayload)
      console.log("Edit successful:", response);
      this.toast?.showSuccessToast('Safety Record Updated successfully!');
      this.closeEditModal();
      await this.getTrainingData(); // Reload data after edit
    } catch (error: any) {
      this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to update report.');
    }
    finally { this.createLoading = false; }
    setTimeout(() => {
      this.createLoading = false;
      this.closeEditModal();
    }, 1000);

  }
  async submitCreateForm(): Promise<void> {
    
    this.trimFormControls(this.createForm!.form);
    this.createForm?.form.markAllAsTouched();
    if (this.createForm?.invalid || this.dateErrorMessage) {
      console.log("Edit form invalid or no data.");
      return;
    }
    this.createLoading = true;
    const additionalData = {
      id: 0,
      enabled: true,
      isDeleted: false,
      // type: 0,
      status: 0,
      adminId: this.currentUser?.id,
    };
    console.log("Submitting edit form with data:", this.createForm?.value);
    console.log("Additional data:", this.newRecordData);
    let apiPayload ={};
    if (this.currentUser.adminsRole.name == 'plantadmin') {
      let data = {

        plantTypeId: this.adminData?.plantType.id,
        plantId: this.currentUser.plant[0].id,
        clusterId: this.adminData?.cluster.id,
        companyName: this.adminData?.opco.title,
      }
      apiPayload = {
        ...this.createForm?.value,
        ...additionalData,
        ...data,
      };
    }
    else {
      apiPayload = {
        ...this.createForm?.value,
        ...additionalData,
      };
    }
   

    console.log(`Submitting Edit. Final API Payload:`, JSON.stringify(apiPayload, null, 2));

    try {
      const response = await this.safetyTrainingService.createSafetyTraining(apiPayload)
      console.log("Edit successful:", response);
      this.toast?.showSuccessToast('Report Added successfully!');
      this.closeCreateModal();
      await this.getTrainingData();
    } catch (error: any) {
      this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to update report.');
    }
    finally { this.createLoading = false; }
    setTimeout(() => {
      this.createLoading = false;
      this.closeCreateModal();
      // this.loadSafetyTrainingRecords(); // Reload data after edit
    }, 1000);
  }

  calculateTotals(data: SafetyTrainingRecord | null): void { // Changed param type
    if (!data) return;
    const N = this.safeNumber;
    data.trainingProgDuration = Number((N(data.trainingProgDuration)).toFixed(2))
    data.totalCompanyMale = Number((N(data.companyMaleManagement) + N(data.companyMaleSFA)));
    data.totalCompanyFemale = Number((N(data.companyFemaleSFA) + N(data.companyFemaleManagement)));
    data.totalNoOfCompanyEmpParticipant = Number((N(data.totalCompanyFemale) + N(data.totalCompanyMale)));
    data.totalContractEmpParticipant = Number((N(data.contractMale) + N(data.contractFemale)));
    data.totalParticpant = Number((N(data.totalNoOfCompanyEmpParticipant) + N(data.totalContractEmpParticipant)));
    data.companyEmpManhour = Number((N(data.totalNoOfCompanyEmpParticipant) * N(data.trainingProgDuration)).toFixed(2));
    data.contractEmpManhour = Number((N(data.totalContractEmpParticipant) * N(data.trainingProgDuration)).toFixed(2));
    data.totalManHour = Number((N(data.companyEmpManhour) + N(data.contractEmpManhour)).toFixed(2));

    this.cdr.detectChanges();
  }

  // --- ADDED: Method to be called from template on change ---
  onBreakdownFieldChange(): void {
    if (this.isCreateModalOpen && this.newRecordData) {
      this.calculateTotals(this.newRecordData);
    } else if (this.isEditModalOpen && this.editingRecordData) {
      this.calculateTotals(this.editingRecordData);
    }
  }
  private safeNumber(value: any): number {
    const num = Number(value);
    return isNaN(num) ? 0 : num;
  }

  async getClusters(): Promise<void> {
    console.log('Fetching clusters...');
    try {
      const clusterParams = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(clusterParams);
      const response = await this.clusterService.getCluster(axiosConfig);
      this.availableClusters = response?.data ?? response ?? [];
      console.log('Clusters loaded:', this.availableClusters);
    } catch (error) {
      console.error("Error fetching clusters:", error);
      this.availableClusters = [];
      // this.toast?.showErrorToast('Failed to load cluster list.');
    }
  }

  async getFacilities(): Promise<void> {
    console.log('Fetching companies (OpCos)...');
    try {
      const companyParams = { sort: 'title,ASC', filter: ['enabled||eq||true','isDeleted||eq||false'], limit: 1000 };
      const axiosConfig = createAxiosConfig(companyParams);
      const response = await this.opcoService.getFacility(axiosConfig);
      this.availableFacilities = response?.data ?? response ?? [];
      console.log('Companies loaded:', this.availableFacilities);
    } catch (error) {
      console.error("Error fetching companies:", error);
      this.availableCompanies = [];
      // this.toast?.showErrorToast('Failed to load company list.');
    }
  }

  async getPlants(): Promise<void> {
    const plantParams: any = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };

    // Add cluster filter if clusterId is selected
    //  if (this.filters.clusterId !== null && this.filters.clusterId !== undefined) {
    //    plantParams.filter.push(`clusterId||eq||${this.filters.clusterId}`);
    //    console.log("Filtering plants by Cluster ID:", this.filters.clusterId);
    //  }

    let allEnabledPlants: Plant[] = [];
    try {
      const axiosConfig = createAxiosConfig(plantParams);
      const response = await this.plantService.getPlants(axiosConfig);
      allEnabledPlants = response?.data ?? response ?? [];

      if (!Array.isArray(allEnabledPlants)) {
        console.error("Received non-array response for plants:", allEnabledPlants);
        allEnabledPlants = [];
      }
    } catch (error) {
      console.error("Error fetching plants:", error);
      this.availablePlants = [];
      // Assuming a toast service is available or implement simple console error
      // this.toast?.showErrorToast('Failed to load plant list.');
    }

    // Filter plants for PLANT_ADMIN based on assigned plant IDs
    if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
      console.log("Filtering plants for PLANT_ADMIN. Allowed Plant IDs:", this.loggedInPlantIds);
      allEnabledPlants = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
      console.log("Filtered plants for PLANT_ADMIN:", allEnabledPlants.map(p => p.name));
    }

    this.availablePlants = allEnabledPlants;
    // Reset plant multi-select if available plants change significantly
    //  this.filters.plantIds = null;
    //  this.isAllPlantsSelected = false;
  }
  private setCurrentUserRoleAndDetailsById(): void {
    // Keep original implementation
    try {
      const userString = localStorage.getItem('user');
      if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
        console.error("User data not found or is invalid in localStorage."); this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = []; this.toast?.showErrorToast("User session invalid. Please log in again."); return;
      }
      const currentUser = JSON.parse(userString); this.loggedInAdminId = currentUser?.id ?? null;
      this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0) ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
      // Retrieve and store the user's department ID
      // this.loggedInUserDepartmentId = currentUser?.departmentId ?? null;
      // console.log(`User Department ID: ${this.loggedInUserDepartmentId || 'None'}`); // Log the department ID
      const roleId = currentUser?.adminsRoleId; if (roleId === 1) { this.currentUserRole = this.componentRoles.SUPER_ADMIN; }
      else if (roleId === 2) { this.currentUserRole = this.componentRoles.PLANT_ADMIN; if (this.loggedInPlantIds.length === 0) { console.error(`Plant Admin (Role ID ${roleId}, User ID ${this.loggedInAdminId}) has no plants assigned!`); this.toast?.showErrorToast("User configuration error: Plant Admin has no assigned plants."); } }
      else { console.error(`Invalid or missing adminsRoleId (${roleId}) for User ID ${this.loggedInAdminId}.`); this.currentUserRole = ''; this.toast?.showErrorToast("User configuration error: Invalid role."); }
      console.log(`User Role Determined: ${this.currentUserRole || 'None'}, Plant IDs: [${this.loggedInPlantIds.join(', ')}]`);
      this.loadSafetyTrainingRecords()
    } catch (error) { console.error("Error parsing user data from localStorage:", error); this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = []; this.toast?.showErrorToast("Error reading user session. Please log in again."); }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      console.log(this.createForm?.controls); // Now controls should be populated
    });
  }
  allowOnlyInteger(event: KeyboardEvent) {
  // Prevent typing dot, comma, e, etc.
  if (['.', ',', 'e', 'E', '+', '-'].includes(event.key)) {
    event.preventDefault();
  }
}


  async getTrainingData() {
    // if (this.listLoading) return;
    this.isLoading = true; // Indicate loading
    this.cdr.detectChanges();

    const filterParams: string[] = [];

    // --- Apply Role-Based Plant Filtering ---
    if (this.currentUser.adminsRole.name === 'plantadmin') {
      if (this.loggedInPlantIds.length > 0) {
        if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
          // Plant Admin selected a specific plant from their assigned list
          // Assuming UI prevents selecting unassigned plants, directly filter by the selected plant ID
          filterParams.push(`plantId||eq||${this.filters.plantId}`);
          console.log("Plant Admin: Filtering tasks by selected plant ID:", this.filters.plantId);
        } else {
          // Plant Admin selected "All My Plants" (or default) - filter by all their assigned plants
          filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
          console.log("Plant Admin: Filtering tasks by assigned plant IDs:", this.loggedInPlantIds);
        }
      } else {
        // Plant Admin has no plants assigned - apply a filter that returns no data
        console.warn("Plant Admin has no assigned plants. Applying impossible filter.");
        filterParams.push(`plantId||eq||-1`);
      }
    } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
      // Super Admin: Only filter if they explicitly selected a plant
      if (this.filters.plantId !== null && this.filters.plantId !== undefined) {
        filterParams.push(`plantId||eq||${this.filters.plantId}`);
        console.log("Super Admin: Filtering tasks by selected plant ID:", this.filters.plantId);
      } else {
        console.log("Super Admin: No specific plant selected, showing all.");
        // No plant filter added
      }
    } else {
      // No role or unknown role - potentially restrict or show all? Showing all for now.
      console.warn("Unknown user role, not applying specific plant filters.");
      // No plant filter added by default
    }
    // --- End Role-Based Plant Filtering ---

    if (this.filters.enabled !== null && this.filters.enabled !== undefined) { filterParams.push(`enabled||eq||${this.filters.enabled}`); }
    if (this.filters.opcoId) { filterParams.push(`opcoId||eq||${this.filters.opcoId}`); }
    if (this.filters.plantTypeId) { filterParams.push(`plantTypeId||eq||${this.filters.plantTypeId}`); }
    if (this.filters.clusterId) { filterParams.push(`clusterId||eq||${this.filters.clusterId}`); }
    if (this.filters.companyName) { filterParams.push(`companyName||eq||${this.filters.companyName}`); }
    // if (this.filters.month) { filterParams.push(`month||eq||${this.filters.month}`); }


    const dataRequest = {
      limit: this.itemsPerPage, // Apply pagination limit
      offset: (this.currentPage - 1) * this.itemsPerPage, // Apply pagination offset
      sort: 'id,DESC',
      filter: filterParams,
    };

    try {
      const param = createAxiosConfig(dataRequest);
      console.log('API Request Params (Safety Training - Paginated):', JSON.stringify(param, null, 2));
      const response = await this.safetyTrainingService.getSafetyTraining(param);

      // Assuming the API returns data in a structure like { data: [...], total: ... }
      if (response && response.data && response.total !== undefined) {
        this.safetyTrainingRecords = response.data;
        this.totalItems = response.total;
        console.log(`Fetched ${response.data.length} records, Total items: ${response.total}`);
      } else if (response && Array.isArray(response)) {
         // Handle direct array response (less ideal for pagination, but handle if necessary)
         this.safetyTrainingRecords = response;
         this.totalItems = response.length; // Assuming the array is the full dataset if no total is provided
         console.log(`Fetched ${response.length} records (direct array response). Assuming this is the total.`);
      }
       else {
        console.warn("Received unexpected response structure for paginated data:", response);
        this.safetyTrainingRecords = [];
        this.totalItems = 0;
      }
    } catch (error: any) {
      console.error("Error fetching paginated safety training records:", error);
      this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve safety training records.");
      this.safetyTrainingRecords = [];
      this.totalItems = 0;
    } finally {
      this.isLoading = false; // Hide loading indicator
    }
  }
  onTrainingTypeChange(trainingType: any) {
    if (trainingType == 1) {
      if (this.isEditModalOpen && this.editingRecordData) {
        this.editingRecordData.trainingType = 0
      }
      else {
        this.newRecordData.trainingType = 0
      }

    }
  }
  validateDate(value: string) {
  const selectedDate = new Date(value);
  const min = new Date(this.minDate);
  const max = new Date(this.maxDate);

  if (!value) {
    // this.dateErrorMessage = 'Date of Training Program is required.';
  } else if (selectedDate < min ||selectedDate > max) {
    this.dateErrorMessage = `Select date from last 2 months up to today.`;
  // } else if (selectedDate > max) {
  //   this.dateErrorMessage = `Date cannot be later than current date.`;
  } else {
    this.dateErrorMessage = '';
  }
}
  async getPlantAdminData() {
    const filterParams: string[] = [];
    filterParams.push(`id||eq||${this.currentUser.plant[0].id}`);
    const data = {
      page: 10,
      sort: `${this.filters.sortField || 'name'},${this.filters.sortDirection || 'ASC'}`,
      filter: filterParams,
      join: ['plantType', 'cluster', 'opco', 'plantAdmin', 'plant'] // Ensure joins match display needs
    };
    console.log("API Request Params (loadPlants):", JSON.stringify(data, null, 2)); // Log final params

    try {
      const param = createAxiosConfig(data);
      const response = await this.plantService.getPlants(param);
      this.adminData = response[0];
      console.log('newrecord', this.newRecordData)
    } catch (error) {
      console.error("Error fetching plants:", error);
      this.toast?.showErrorToast('Failed to load plants.');
    } finally {
    }

  }
}
