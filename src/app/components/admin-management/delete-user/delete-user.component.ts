import { CommonModule } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild, inject, AfterViewInit, OnDestroy } from '@angular/core'; // Added inject, AfterViewInit, OnDestroy
import { FormsModule, NgForm } from '@angular/forms'; // Import FormsModule and NgForm
import { AdminService } from '../../../services/admin/admin.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { DepartmentService } from '../../../services/master-management/department/department.service';
import { DesignationService } from '../../../services/master-management/designation/designation.service';
import { PlantManagementService } from '../../../services/plant-management/plant-management.service';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { Modal } from 'bootstrap';
import { DeleteService } from '../../../services/delete/delete.service';

// --- Define ROLES constant ---
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// Interface for filter structure
interface DeletedUserFilter {
    firstName?: string | null;       // Filtering on admin.firstName
    lastName?: string | null;        // Filtering on admin.lastName
    email?: string | null;           // Filtering on admin.email
    mobile?: string | null;          // Filtering on admin.contactNumber
    plantId?: number | null;         // Filtering on admin.plant.id
    designationId?: number | null;   // Filtering on admin.designationId
    departmentId?: number | null;    // Filtering on admin.departmentId
    adminRoleId?: number | null;     // Filtering on admin.adminsRole.id
    sortField?: string | null;       // Field to sort by (log or nested admin)
    sortDirection?: 'ASC' | 'DESC';
}

// --- Interfaces for dropdown data ---
interface Plant { id: number; name: string; }
interface Designation { id: number; title: string; }
interface Department { id: number; title: string; }
interface AdminRole { id: number; name: string; }

// --- Deleted User Log Item Interface ---
interface DeletedUserLogItem {
    id: number; // Log entry ID
    adminId: number; // ID of the user who was deleted
    createdBy?: number; // ID of the admin who performed the deletion
    createdAt?: string | Date; // Timestamp of deletion log entry
    status?: number; // Status of the log entry (or the user at the time of deletion, e.g., 3)
    updatedAt?: string | Date; // Timestamp of re-activation (if applicable)
    // --- Nested Objects ---
    admin: { // Details of the user who was deleted
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        contactNumber: string | null;
        dob?: string | null; // Optional
        gender?: number | string | null; // Allow both
        profilePicture?: string | null; // Optional
        adminsRole?: { id: number; name: string }; // Nested Role
        department?: { id: number; title: string }; // Nested Department
        designation?: { id: number; title: string }; // Nested Designation
        // --- Plants Array ---
        // Assuming API returns plants linked to the user via plantUsers join when fetching admin details
        plant?: { id: number; name: string }[]; // Use plantUsers structure if that's how plants are linked
    };
    // Re-activator details might not be directly on the log, but useful if API provides it
    activatedByAdmin?: {
        id: number;
        firstName: string;
        lastName: string;
    } | null;
}


@Component({
    selector: 'app-delete-user',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './delete-user.component.html',
    styleUrl: './delete-user.component.scss'
})
export class DeleteUserComponent implements OnInit, AfterViewInit, OnDestroy { // Implement interfaces
    // --- Expose ROLES to the template ---
    public componentRoles = ROLES;

    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    @ViewChild('reactivateConfirmationModalElement') reactivateConfirmationModalElement!: ElementRef;
    @ViewChild('filterForm') filterForm!: NgForm;

    reactivateConfirmationModalInstance: Modal | null = null;

    // --- State Variables ---
    itemToReactivate: any | null = null;
    isFilterOffcanvasOpen = false; // Renamed for clarity
    deletedUserList: any[] = []; // Use specific type
    listLoading = false;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // Pagination
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // --- Filter Properties ---
    filters: DeletedUserFilter = {
        firstName: null, lastName: null, email: null, mobile: null,
        plantId: null, designationId: null, departmentId: null, adminRoleId: null,
        sortField: 'id',        // Default sort field (Log ID)
        sortDirection: 'DESC'
    };

    // --- RBAC Properties ---
    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = [];

    // --- Data for Filter Dropdowns ---
    availablePlants: Plant[] = []; // Use specific type, will be filtered by role
    availableDesignations: any[] = []; // Use specific type
    availableDepartments: any[] = [];   // Use specific type
    availableAdminRoles: AdminRole[] = [ // Use specific type
        { id: 3, name: 'User' }, { id: 2, name: 'Plant Admin' }, { id: 1, name: 'Super Admin' }
    ];
    // Adjust sort fields based on actual nested properties and API capabilities
    availableSortFields = [
        { value: 'id', label: 'Log ID' },
        { value: 'admin.firstName', label: 'First Name' },
        { value: 'admin.lastName', label: 'Last Name' },
        { value: 'admin.email', label: 'Email' },
        { value: 'createdAt', label: 'Deleted Date' } // Sort by log creation date
    ];

    // --- Service Injection ---
    private adminService = inject(AdminService);
    private plantService = inject(PlantManagementService);
    private designationService = inject(DesignationService);
    private departmentService = inject(DepartmentService);
    private deleteService = inject(DeleteService);

    constructor() { } // Keep empty

    ngOnInit() {
        this.setCurrentUserRoleAndDetailsById(); // Set role FIRST
        // Fetch dropdowns (plants will be filtered based on role)
        this.getPlants();
        this.getDesignations();
        this.getDepartments();
        // Load initial data (will apply RBAC filters)
        this.loadDeletedUsers(this.currentPage);
    }

    ngAfterViewInit(): void {
        if (this.reactivateConfirmationModalElement) {
            this.reactivateConfirmationModalInstance = new Modal(this.reactivateConfirmationModalElement.nativeElement);
        } else {
            console.error("Re-activate confirmation modal element not found!");
        }
    }

    ngOnDestroy(): void {
        this.reactivateConfirmationModalInstance?.dispose();
    }

     // --- RBAC Setup ---
     private setCurrentUserRoleAndDetailsById() {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];

            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1) { this.currentUserRole = ROLES.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) {
                    console.warn("Plant Admin has no assigned plants. Deleted user visibility might be limited.");
                }
            } else {
                this.currentUserRole = ''; // Or handle other roles
                console.warn(`User has role ID ${roleId}, which may not have permissions for deleted users.`);
            }
            console.log(`DeleteUser - Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

    // --- Helper to get current list data ---
    getCurrentListData(): DeletedUserLogItem[] | undefined {
        // Access the list directly, ensuring type safety
        if (Array.isArray(this.deletedUserList)) {
            return this.deletedUserList;
        }
        return undefined; // Should not happen if initialized correctly
    }

    // --- Re-activation Confirmation Modal ---
    openReactivateConfirmation(logEntry: any): void {
        if (!logEntry || logEntry.id === undefined) {
            console.error('Cannot open re-activate confirmation: Invalid log entry data.', logEntry);
            this.toast?.showErrorToast("Invalid data for re-activation.");
            return;
        }

        // --- RBAC Check: Only Super Admin can re-activate (Plant Admins usually cannot undelete) ---
        if (this.currentUserRole !== ROLES.SUPER_ADMIN && this.currentUserRole !== ROLES.PLANT_ADMIN) {
            this.toast?.showErrorToast("You do not have permission to re-activate users.");
            return;
        }
        // --- End RBAC Check ---

        console.log('Opening re-activate confirmation for log entry:', logEntry);
        this.itemToReactivate = logEntry;
        this.reactivateConfirmationModalInstance?.show();
    }

    confirmReactivate(): void {
        if (!this.itemToReactivate || !this.itemToReactivate.id) {
            console.error('Confirmation failed: No item or ID stored for re-activation.');
            this.toast?.showErrorToast("An internal error occurred. Please try again.");
            this.closeReactivateConfirmation();
            return;
        }

        // --- RBAC Check (again) ---
        if (this.currentUserRole !== ROLES.SUPER_ADMIN && this.currentUserRole !== ROLES.PLANT_ADMIN) {
            this.toast?.showErrorToast("Permission denied.");
            this.closeReactivateConfirmation();
            return;
        }
        // --- End RBAC Check ---

        const logEntryToProcess = this.itemToReactivate;
        this.closeReactivateConfirmation();
        this.undoDeleteFormSubmit(logEntryToProcess); // Call the action method
    }

    closeReactivateConfirmation(): void {
        this.reactivateConfirmationModalInstance?.hide();
        this.itemToReactivate = null;
    }

    // --- Excel Download ---
    async fetchAllFilteredDeletedUsers(): Promise<DeletedUserLogItem[] | null> {
        this.listLoading = true;
        // Use the updated buildFilterParams which includes type conversion and correct path
        const filterParams = this.buildFilterParams(true); // Pass true to indicate fetching all

        const data = {
            limit: 10000,
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            // Join relations based on the API response structure (same as in loadDeletedUsers)
            join: ['admin', 'plant', 'department', 'designation', 'adminsRole', 'activatedByAdmin']
        };

        try {
            const param = createAxiosConfig(data);
            console.log('[fetchAllFilteredDeletedUsers] Final API Request Params:', JSON.stringify(param, null, 2));
            const response = await this.adminService.getDeletedUserLog(param);
            return response?.data ?? response ?? [];
        } catch (error: any) {
            console.error("Error fetching all deleted user logs for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
            this.listLoading = false;
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return;

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} deleted user logs...`);

        let dataToExport: DeletedUserLogItem[] | null = null;

        try {
            if (type === 'all') { dataToExport = await this.fetchAllFilteredDeletedUsers(); }
            else { dataToExport = this.getCurrentListData() ?? null; }

            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No deleted user logs available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} logs for Excel export (${type}).`);

            const dataForExcel = dataToExport.map(item => {
                // Use type assertion to tell TypeScript that the item has these properties
                // This is necessary because the interface definition doesn't match the actual data structure
                const actualItem = item as any;

                // Use the root item object for user details as shown in the HTML template
                const plants = actualItem.plant?.map((pu: any) => pu.name).filter(Boolean).join(', ') || 'N/A';

                // Format gender display
                const genderDisplay =
                    actualItem.gender === 0 ? 'Female' :
                    actualItem.gender === 1 ? 'Male' :
                    actualItem.gender === 2 ? 'Other' :
                    (actualItem.gender || 'N/A');

                // Format date
                const formattedDob = actualItem.dob ? new Date(actualItem.dob).toLocaleDateString() : 'N/A';

                return {
                    // User Detail Column
                    'Name': `${actualItem.firstName || ''} ${actualItem.lastName || ''}`.trim() || 'N/A',
                    'DOB': formattedDob,
                    'Gender': genderDisplay,
                    'Role': actualItem.adminsRole?.name || 'N/A',
                    'Email': actualItem.email || 'N/A',
                    'Contact': actualItem.contactNumber || 'N/A',

                    // Plant Information Column
                    'Plants': plants,
                    'Department': actualItem.department?.title || 'N/A',
                    'Designation': actualItem.designation?.title || 'N/A',

                    // Deleted By Column - Only ID and Name
                    'Deleted By ID': actualItem.adminId || 'N/A',
                    'Deleted By Name': actualItem.admin ? `${actualItem.admin.firstName || ''} ${actualItem.admin.lastName || ''}`.trim() : 'N/A',
                    'Deleted On': actualItem.createdTimestamp ? new Date(actualItem.createdTimestamp).toLocaleString() : 'N/A',
                };
            });

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Deleted Users');

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Deleted_Users_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Dropdown Data Fetching (with RBAC for Plants) ---
    async getDesignations() {
        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        try {
            const param = createAxiosConfig(data);
            const response = await this.designationService.getDesignation(param);
            this.availableDesignations = response?.data ?? response ?? [];
        } catch (error) {
            console.error("Error fetching designations:", error);
            this.availableDesignations = [];
            this.toast?.showErrorToast("Failed to load designations.");
        }
    }

    async getDepartments() {
        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        try {
            const param = createAxiosConfig(data);
            const response = await this.departmentService.getDepartments(param);
            this.availableDepartments = response?.data ?? response ?? [];
        } catch (error) {
            console.error("Error fetching departments:", error);
            this.availableDepartments = [];
            this.toast?.showErrorToast("Failed to load departments.");
        }
    }

    async getPlants() {
        const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        let allEnabledPlants: Plant[] = [];
        try {
            const param = createAxiosConfig(data);
            const response = await this.plantService.getPlants(param);
            allEnabledPlants = response?.data ?? response ?? [];
        } catch (error) {
            console.error("Error fetching plants:", error);
            this.availablePlants = [];
            this.toast?.showErrorToast('Failed to load plants for filter.');
            return;
        }

        // --- Filter available plants based on role ---
        if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
            this.availablePlants = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            this.availablePlants = allEnabledPlants;
        } else {
            this.availablePlants = []; // For roles other than Super Admin or Plant Admin, show no plants.
        }
        console.log(`Loaded ${this.availablePlants.length} plants accessible to current user for filter.`);
    }

    // --- Filter Modal ---
    openFilterModal() {
        this.isFilterOffcanvasOpen = true;
    }
    closeFilterModal() { // Renamed from closeModal
        this.isFilterOffcanvasOpen = false;
    }

        // --- Build Filter Params with RBAC ---
        private buildFilterParams(isFetchingAll = false): string[] {
            // Base filters - adjust based on API behavior for deleted logs
            const filterParams: string[] = ['status||eq||3']; // Assuming log status reflects user status

            // Define the correct plant filter path based on the API response structure
            const plantFilterPath = 'plant.id';

            // --- RBAC Plant Filtering (applied directly to the item) ---
            if (this.currentUserRole === ROLES.PLANT_ADMIN) {
                if (this.loggedInPlantIds.length > 0) {

                    // *** START FIX ***
                    // Convert the selected filter plant ID to a number for reliable comparison
                    const selectedPlantIdNum = this.filters.plantId != null ? Number(this.filters.plantId) : null;

                    // Add logging to verify types and values just before the check
                    console.log('[buildFilterParams - DeletedUser] Checking Plant Filter:');
                    console.log('  Raw filters.plantId:', this.filters.plantId, typeof this.filters.plantId);
                    console.log('  Converted selectedPlantIdNum:', selectedPlantIdNum, typeof selectedPlantIdNum);
                    console.log('  loggedInPlantIds:', this.loggedInPlantIds);
                    console.log('  Includes Check Result:', selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum));

                    // Check if a specific plant is selected AND managed by admin (using the number)
                    if (selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum)) {
                        // A specific, valid plant IS selected - filter ONLY by this plant
                        filterParams.push(`${plantFilterPath}||eq||${selectedPlantIdNum}`); // Use number and corrected path
                        console.log(`[buildFilterParams - DeletedUser] Applying EQ filter for plant: ${selectedPlantIdNum}`);
                    } else {
                        // Filter by ANY plants managed by admin
                        filterParams.push(`${plantFilterPath}||$in||${this.loggedInPlantIds.join(',')}`); // Use corrected path
                        console.log(`[buildFilterParams - DeletedUser] Applying IN filter for plants: ${this.loggedInPlantIds.join(',')}`);

                        // Reset visual filter if needed
                        if (this.filters.plantId != null && (selectedPlantIdNum == null || !this.loggedInPlantIds.includes(selectedPlantIdNum))) {
                             if (!isFetchingAll) {
                                 console.warn(`[buildFilterParams - DeletedUser] Invalid or non-managed plant filter (${this.filters.plantId}) detected, resetting UI filter.`);
                                 this.filters.plantId = null;
                             }
                             // Also reset form model for consistency when opening filter again
                             // Assuming filters object is bound two-way or needs manual reset here if not
                        }
                    }
                     // *** END FIX ***

                } else {
                    // Block data if Plant Admin has no plants
                    filterParams.push(`${plantFilterPath}||eq||-1`); // Use corrected path
                    console.log('[buildFilterParams - DeletedUser] Plant Admin has no plants, blocking results.');
                }
            } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
                // Super Admin can filter by any specific plant
                if (this.filters.plantId != null) { // Check specifically for not null
                    filterParams.push(`${plantFilterPath}||eq||${this.filters.plantId}`); // Use corrected path
                    console.log(`[buildFilterParams - DeletedUser] Super Admin applying EQ filter for plant: ${this.filters.plantId}`);
                } else {
                    console.log('[buildFilterParams - DeletedUser] Super Admin showing all plants.');
                }
            }
            // --- End RBAC Plant Filtering ---

            // Add other dynamic filters (applied directly to the item based on API response)
            if (this.filters.firstName) filterParams.push(`firstName||$contL||${this.filters.firstName}`);
            if (this.filters.lastName) filterParams.push(`lastName||$contL||${this.filters.lastName}`);
            if (this.filters.email) filterParams.push(`email||$contL||${this.filters.email}`);
            if (this.filters.mobile) filterParams.push(`contactNumber||$contL||${this.filters.mobile}`);

            // Plant filter handled above by RBAC logic

            if (this.filters.designationId !== null) filterParams.push(`designationId||eq||${this.filters.designationId}`);
            if (this.filters.departmentId !== null) filterParams.push(`departmentId||eq||${this.filters.departmentId}`);
            if (this.filters.adminRoleId !== null) filterParams.push(`adminsRoleId||eq||${this.filters.adminRoleId}`);

            return filterParams;
        }

    // --- Centralized Data Loading (with RBAC) ---
    async loadDeletedUsers(page: number) {
        this.listLoading = true;
        this.deletedUserList = [];
        const filterParams = this.buildFilterParams(); // Build filters including RBAC

        const data = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
             // Join relations based on the API response structure
             join: ['admin', 'plant', 'department', 'designation', 'adminsRole', 'activatedByAdmin']

        };

        try {
            const param = createAxiosConfig(data);
            console.log('[loadDeletedUsers] Final API Request Params:', JSON.stringify(data, null, 2)); // Debug log
            const response = await this.adminService.getDeletedUserLog(param);
            // Need to make sure response mapping handles the plant data correctly based on the join used
            this.deletedUserList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error: any) {
            // ... (rest of catch/finally)
             console.error("Error fetching deleted user log:", error);
            this.deletedUserList = [];
            this.totalItems = 0;
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load deleted user logs.');
        } finally {
            this.listLoading = false;
        }
    }

    // --- Pagination ---
    onPageChange(page: number) {
        if (this.currentPage === page || this.listLoading) return;
        this.currentPage = page;
        this.loadDeletedUsers(this.currentPage);
    }

    // --- Filter Actions ---
    applyFilters() {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        // Validate email format if provided
        if (this.filters.email) {
            // First check basic email format
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailRegex.test(this.filters.email)) {
                this.toast?.showErrorToast("Please enter a valid email address.");
                return;
            }

            // Then check for adani.com domain
            if (!this.filters.email.toLowerCase().endsWith('@adani.com')) {
                this.toast?.showErrorToast("Email must use the @adani.com domain.");
                return;
            }
        }

        // Trim string values to prevent whitespace-only searches
        if (this.filters.firstName) this.filters.firstName = this.filters.firstName.trim();
        if (this.filters.lastName) this.filters.lastName = this.filters.lastName.trim();
        if (this.filters.email) this.filters.email = this.filters.email.trim();
        if (this.filters.mobile) this.filters.mobile = this.filters.mobile.trim();

        this.currentPage = 1;
        this.loadDeletedUsers(this.currentPage);
        this.closeFilterModal();
    }

    resetFilters() {
        this.filters = {
            firstName: null, lastName: null, email: null, mobile: null,
            plantId: null, designationId: null, departmentId: null, adminRoleId: null,
            sortField: 'id',
            sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.loadDeletedUsers(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- Sorting Helpers ---
    getSortClass(key: string): string {
        if (this.filters.sortField === key) {
            return this.filters.sortDirection === 'ASC' ? 'sort-asc' : 'sort-desc';
        }
        return 'sort-none'; // Return specific class for non-sorted state if needed
    }

    sortBy(field: string) {
        if (this.listLoading) return;
        if (this.filters.sortField === field) {
            this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC';
        } else {
            this.filters.sortField = field;
            this.filters.sortDirection = 'DESC';
        }
        this.currentPage = 1; // Reset to page 1 on sort change
        this.loadDeletedUsers(this.currentPage); // Reload data
    }

    // --- Re-activation Action ---
    async undoDeleteFormSubmit(logEntry: any): Promise<void> {
        // RBAC check performed in confirmReactivate
        const userId = logEntry.id;
        const userFullName = `${logEntry.firstName ?? ''} ${logEntry.lastName ?? ''}`.trim();

        if (!userId) {
            this.toast?.showErrorToast("Error: Could not find user ID to re-activate.");
            return;
        }

        this.listLoading = true;
        try {
            // Use the deleteService with the specified payload structure
            const payload = {
                tableName: 'deleted-user-log',
                id: logEntry.id // Use the ID of the deleted user log entry
            };

            // Use the deleteService instead of adminService.reactivateUser
            await this.deleteService.deleteData(payload);

            this.toast?.showSuccessToast(`User ${userFullName} (ID: ${userId}) re-activated successfully.`);
            // Handle pagination if the last item on the page is reactivated (removed)
            if (this.deletedUserList.length === 1 && this.currentPage > 1) {
                this.currentPage--;
            }
            this.loadDeletedUsers(this.currentPage); // Refresh the log list

        } catch (error: any) {
            console.error(`Error re-activating user ID: ${userId}`, error);
            this.toast?.showErrorToast(error?.response?.data?.message || 'An error occurred during re-activation.');
        } finally {
            this.listLoading = false;
        }
    }

    // --- Display Helper ---
    showMorePlants(logEntry: any): void {
        // Access plants directly from the item
        const plantNames = logEntry.plant
            ?.map((p: any) => p.name)
            .filter(Boolean) // Remove null/undefined names
            .join('\n') || 'None assigned';

        const userFullName = `${logEntry.firstName ?? ''} ${logEntry.lastName ?? ''}`.trim();
        // Use a modal for better display instead of alert
        alert(`Assigned Plants for ${userFullName}:\n\n${plantNames}`);
    }

    // Helper method to safely get plant name
    getPlantName(plant: any): string {
        return plant && plant.name ? plant.name : 'Unknown Plant';
    }
}
